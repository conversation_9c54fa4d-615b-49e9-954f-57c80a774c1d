{% extends 'base.html' %}
{% load static %}
{% load report_extras %}

{% block title %}
  {% if client %}
    Client Order Schedule - {{ client.name }}
  {% else %}
    Client Order Schedule
  {% endif %}- RadioMention
{% endblock %}

{% block extra_css %}
  <style>
    @media print {
      .no-print {
        display: none !important;
      }
      .page-break-inside-avoid {
        page-break-inside: avoid;
      }
      .schedule-table {
        font-size: 8px;
      }
      .schedule-table th,
      .schedule-table td {
        padding: 2px !important;
        border: 1px solid #000 !important;
      }
      body {
        font-size: 10px;
      }
      .client-info {
        margin-bottom: 15px;
      }
    }
    
    .schedule-table {
      font-size: 11px;
    }
    
    .schedule-table th {
      background-color: #f8f9fa;
      font-weight: 600;
      text-align: center;
      padding: 8px 4px;
      border: 1px solid #dee2e6;
    }
    
    .schedule-table td {
      padding: 4px;
      border: 1px solid #dee2e6;
      text-align: center;
      vertical-align: middle;
    }
    
    .mention-slot {
      background-color: #e3f2fd;
      border-radius: 3px;
      padding: 2px;
      margin: 1px;
      font-size: 10px;
      line-height: 1.2;
    }
    
    .mention-slot.completed {
      background-color: #c8e6c9;
    }
    
    .mention-slot.pending {
      background-color: #fff3e0;
    }
    
    .time-header {
      min-width: 60px;
      font-size: 11px;
      text-align: center;
    }
    
    .date-header {
      min-width: 100px;
      text-align: center;
    }
    
    .summary-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 1.5rem;
    }
    
    .stat-card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 1rem;
      text-align: center;
    }
    
    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      color: #1f2937;
    }
    
    .stat-label {
      font-size: 0.875rem;
      color: #6b7280;
      margin-top: 0.25rem;
    }
  </style>
{% endblock %}

{% block content %}
  <div class="max-w-full mx-auto">
    {% if show_client_selection %}
      <!-- Client Selection Form -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h1 class="text-xl font-semibold text-gray-900">Client Order Schedule Report</h1>
          <p class="text-sm text-gray-600 mt-1">Select a client to view their detailed mention schedule</p>
        </div>
        <div class="p-6">
          <form method="get" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="client_id" class="block text-sm font-medium text-gray-700 mb-2">Client</label>
                <select name="client_id" id="client_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                  <option value="">Select a client...</option>
                  {% for client in clients %}
                    <option value="{{ client.id }}">{{ client.name }}</option>
                  {% endfor %}
                </select>
              </div>
              <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                <input type="date" name="start_date" id="start_date" value="{{ start_date|date:'Y-m-d' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" />
              </div>
              <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                <input type="date" name="end_date" id="end_date" value="{{ end_date|date:'Y-m-d' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" />
              </div>
            </div>
            <div class="flex justify-end">
              <button type="submit" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                <i class="fa-solid fa-calendar-days mr-2"></i>
                Generate Schedule
              </button>
            </div>
          </form>
        </div>
      </div>
    {% else %}
      <!-- Header with client info -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-xl font-semibold text-gray-900">{{ client.name }} - Media Schedule</h1>
              <p class="text-sm text-gray-600 mt-1">{{ start_date|date:'F d, Y' }} to {{ end_date|date:'F d, Y' }} ({{ duration_text }})</p>
            </div>
            <div class="flex space-x-2 no-print">
              <!-- PDF Export Button -->
              {% include 'reports/partials/pdf_export_button.html' with report_type='client-order-schedule' %}

              <a href="{% url 'reports:client_order_schedule' %}" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
                <i class="fa-solid fa-arrow-left mr-1"></i>
                Back to Selection
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 no-print">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Schedule</h3>
          <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label for="client_id" class="block text-sm font-medium text-gray-700 mb-2">Client</label>
              {{ filter_form.client_id }}
            </div>
            <div>
              <label for="mention" class="block text-sm font-medium text-gray-700 mb-2">Mention</label>
              {{ filter_form.mention }}
            </div>
            <div>
              <label for="show" class="block text-sm font-medium text-gray-700 mb-2">Show</label>
              {{ filter_form.show }}
            </div>
            <div>
              <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
              {{ filter_form.status }}
            </div>
            <div>
              <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
              {{ filter_form.start_date }}
            </div>
            <div>
              <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
              {{ filter_form.end_date }}
            </div>
            <div class="lg:col-span-4 flex justify-end space-x-3 mt-4">
              <a href="{% url 'reports:client_order_schedule' %}" class="bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400">Clear Filters</a>
              <button type="submit" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                <i class="fa-solid fa-filter mr-2"></i>
                Apply Filters
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Summary Statistics -->
      <div class="summary-stats no-print mb-6">
        <div class="stat-card">
          <div class="stat-number text-blue-600">{{ total_slots }}</div>
          <div class="stat-label">Total Slots</div>
        </div>
        <div class="stat-card">
          <div class="stat-number text-green-600">{{ completed_slots }}</div>
          <div class="stat-label">Completed</div>
        </div>
        <div class="stat-card">
          <div class="stat-number text-orange-600">{{ pending_slots }}</div>
          <div class="stat-label">Pending</div>
        </div>
        <div class="stat-card">
          <div class="stat-number text-purple-600">{{ duration_text }}</div>
          <div class="stat-label">Campaign Duration</div>
        </div>
      </div>

      <!-- Schedule Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 page-break-inside-avoid">
        <div class="px-6 py-4 border-b border-gray-200 no-print">
          <h3 class="text-lg font-medium text-gray-900">Detailed Schedule</h3>
          <p class="text-sm text-gray-600 mt-1">Shows when and where each mention will be read</p>
        </div>
        <div class="overflow-x-auto">
          {% if schedule_data %}
            <table class="min-w-full schedule-table">
              <thead>
                <tr>
                  <th class="date-header">Date</th>
                  {% for time_slot in sorted_times %}
                    <th class="time-header">{{ time_slot|slice:':5' }}</th>
                  {% endfor %}
                  <th class="text-center">Daily Total</th>
                </tr>
              </thead>
              <tbody>
                {% for date, time_data in schedule_data.items %}
                  <tr>
                    <td class="font-medium text-center">{{ date|date:'D m/d' }}</td>
                    {% for time_slot in sorted_times %}
                      <td>
                        {% if time_slot in time_data %}
                          {% for slot_data in time_data|lookup:time_slot %}
                            <div class="mention-slot {% if slot_data.is_completed %}
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                completed

















                              {% else %}
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                pending

















                              {% endif %}">
                              <div class="font-semibold">{{ slot_data.show.name|truncatechars:8 }}</div>
                              <div>{{ slot_data.mention.title|truncatechars:10 }}</div>
                              {% if slot_data.is_completed %}
                                <div class="text-xs text-green-700">✓</div>
                              {% endif %}
                            </div>
                          {% endfor %}
                        {% else %}
                          -
                        {% endif %}
                      </td>
                    {% endfor %}
                    <td class="font-medium">{{ time_data|dict_length }}</td>
                  </tr>
                {% endfor %}
              </tbody>
              <tfoot>
                <tr class="bg-gray-50 font-medium">
                  <td>Total</td>
                  {% for time_slot in sorted_times %}
                    <td>{{ schedule_data|time_slot_count:time_slot }}</td>
                  {% endfor %}
                  <td class="font-bold">{{ total_slots }}</td>
                </tr>
              </tfoot>
            </table>
          {% else %}
            <div class="text-center py-12">
              <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                <i class="fa-solid fa-calendar-xmark text-gray-400 text-xl"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">No Schedule Data</h3>
              <p class="text-gray-500">No scheduled mentions found for {{ client.name }} in the selected date range.</p>
            </div>
          {% endif %}
        </div>
      </div>
    {% endif %}
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Store the current mention filter value
    const currentMentionFilter = '{{ mention_filter|default:"" }}'
    
    // Function to update mention options based on selected client
    function updateMentionOptions(clientId) {
      const mentionSelect = document.getElementById('id_mention')
    
      // Clear current options
      mentionSelect.innerHTML = '<option value="">All Mentions</option>'
    
      if (!clientId) {
        return
      }
    
      // Fetch mentions for the selected client
      fetch(`{% url 'reports:get_client_mentions_api' %}?client_id=${clientId}`)
        .then((response) => response.json())
        .then((data) => {
          data.mentions.forEach((mention) => {
            const option = document.createElement('option')
            option.value = mention.id
            option.textContent = mention.title
    
            // Select the option if it matches the current filter
            if (currentMentionFilter && mention.id == currentMentionFilter) {
              option.selected = true
            }
    
            mentionSelect.appendChild(option)
          })
        })
        .catch((error) => {
          console.error('Error fetching mentions:', error)
        })
    }
    
    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function () {
      const clientSelect = document.getElementById('id_client_id')
      if (clientSelect && clientSelect.value) {
        updateMentionOptions(clientSelect.value)
      }
    })
  </script>
{% endblock %}
