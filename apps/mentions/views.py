from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.contrib import messages
from django.db import models
from django.core.paginator import Paginator
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from datetime import datetime, timedelta
import json

from .models import Mention, MentionReading, RecurringMention, RecurringMentionShow, MentionAuditLog
from .services import SchedulePatternService, ScheduleSplittingService
from .forms import (
    MentionForm, MentionReadingForm, RecurringMentionForm,
    MentionFilterForm, BulkActionForm, QuickScheduleForm,
    RecurringMentionFilterForm
)
from .services import ConflictDetectionService, RecurringMentionService
from apps.core.models import Client, Presenter
from apps.shows.models import Show
from apps.organizations.middleware import get_current_organization
from apps.core.decorators import require_permission, require_any_permission
import logging
import uuid
from django.db import transaction
from django.shortcuts import redirect

logger = logging.getLogger(__name__)


def parse_time_string(time_str):
    """
    Parse time string in either HH:MM:SS or HH:MM format
    Returns time object or raises ValueError
    """
    try:
        # Try HH:MM:SS format first
        return datetime.strptime(time_str, '%H:%M:%S').time()
    except ValueError:
        try:
            # Fall back to HH:MM format
            return datetime.strptime(time_str, '%H:%M').time()
        except ValueError:
            raise ValueError(f"Invalid time format: {time_str}. Expected HH:MM or HH:MM:SS format.")


@login_required
def calendar_view(request):
    """Calendar view for scheduling mentions"""
    from calendar import monthrange
    from apps.core.models import Presenter

    # Get current organization
    organization = get_current_organization(request)

    date_str = request.GET.get('date')
    view = request.GET.get('view', 'day')

    if date_str:
        current_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    else:
        current_date = datetime.now().date()

    # Get schedule for the current date (filtered by organization)
    if organization:
        schedule = MentionReading.objects.filter(
            scheduled_date=current_date,
            mention__client__organization=organization
        ).select_related('mention', 'show', 'presenter', 'mention__client')

        # Get approved mentions that haven't been scheduled to specific time slots yet
        # These are mentions with status='scheduled' but no MentionReading records
        unscheduled_mentions = Mention.objects.filter(
            status='scheduled',
            mentionreading__isnull=True,
            client__organization=organization
        ).select_related('client')

        # Get active shows and presenters (filtered by organization)
        active_shows = Show.objects.filter(is_active=True, organization=organization)
        presenters = Presenter.objects.filter(is_active=True, organization=organization)
    else:
        # Fallback if no organization context
        schedule = MentionReading.objects.filter(
            scheduled_date=current_date
        ).select_related('mention', 'show', 'presenter', 'mention__client')

        unscheduled_mentions = Mention.objects.filter(
            status='scheduled',
            mentionreading__isnull=True
        ).select_related('client')

        active_shows = Show.objects.filter(is_active=True)
        presenters = Presenter.objects.filter(is_active=True)

    # Time slots (24 hours to show all mentions including late night/early morning)
    time_slots = list(range(0, 24))  # Midnight to 11 PM (24 hours)

    # Navigation dates
    if view == 'week':
        # Week navigation
        days_since_monday = current_date.weekday()
        week_start = current_date - timedelta(days=days_since_monday)
        prev_date = week_start - timedelta(days=7)
        next_date = week_start + timedelta(days=7)
    elif view == 'month':
        # Month navigation
        if current_date.month == 1:
            prev_date = current_date.replace(year=current_date.year - 1, month=12, day=1)
        else:
            prev_date = current_date.replace(month=current_date.month - 1, day=1)

        if current_date.month == 12:
            next_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
        else:
            next_date = current_date.replace(month=current_date.month + 1, day=1)
    else:
        # Day navigation
        prev_date = current_date - timedelta(days=1)
        next_date = current_date + timedelta(days=1)

    today = datetime.now().date()

    # Prepare view-specific data
    week_days = []
    month_weeks = []
    day_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']

    if view == 'week':
        # Generate week data
        days_since_monday = current_date.weekday()
        week_start = current_date - timedelta(days=days_since_monday)

        for i in range(7):
            day_date = week_start + timedelta(days=i)
            if organization:
                day_schedule = MentionReading.objects.filter(
                    scheduled_date=day_date,
                    mention__client__organization=organization
                ).select_related('mention', 'show', 'presenter', 'mention__client')
            else:
                day_schedule = MentionReading.objects.filter(
                    scheduled_date=day_date
                ).select_related('mention', 'show', 'presenter', 'mention__client')

            week_days.append({
                'date': day_date,
                'schedule': day_schedule
            })

    elif view == 'month':
        # Generate month data
        year = current_date.year
        month = current_date.month

        # Get first day of month and number of days
        first_day = current_date.replace(day=1)
        days_in_month = monthrange(year, month)[1]

        # Calculate start of calendar (might be previous month)
        start_weekday = first_day.weekday()
        calendar_start = first_day - timedelta(days=start_weekday)

        # Generate 6 weeks of calendar
        for week in range(6):
            week_data = []
            for day in range(7):
                calendar_date = calendar_start + timedelta(days=week * 7 + day)

                # Get schedule for this day (filtered by organization)
                if organization:
                    day_schedule = MentionReading.objects.filter(
                        scheduled_date=calendar_date,
                        mention__client__organization=organization
                    ).select_related('mention', 'show', 'presenter', 'mention__client')[:3]  # Limit to 3 for display
                else:
                    day_schedule = MentionReading.objects.filter(
                        scheduled_date=calendar_date
                    ).select_related('mention', 'show', 'presenter', 'mention__client')[:3]  # Limit to 3 for display

                is_other_month = calendar_date.month != month
                is_today = calendar_date == today

                week_data.append({
                    'date': calendar_date,
                    'day': calendar_date.day,
                    'schedule': day_schedule,
                    'is_other_month': is_other_month,
                    'is_today': is_today
                })
            month_weeks.append(week_data)

    # Get active campaigns for progress tracking
    from .models import RecurringMention
    active_campaigns = []
    if organization:
        active_campaigns = RecurringMention.objects.filter(
            client__organization=organization,
            is_active=True,
            campaign_name__isnull=False
        ).exclude(campaign_name='').select_related('client')

        # Update campaign progress
        for campaign in active_campaigns:
            campaign.update_campaign_progress()

    context = {
        'current_date': current_date,
        'prev_date': prev_date,
        'next_date': next_date,
        'today': today,
        'view': view,
        'schedule': schedule,
        'unscheduled_mentions': unscheduled_mentions,
        'active_shows': active_shows,
        'presenters': presenters,
        'time_slots': time_slots,
        'week_days': week_days,
        'month_weeks': month_weeks,
        'day_names': day_names,
        'organization': organization,
        'active_campaigns': active_campaigns,
    }

    return render(request, 'mentions/calendar.html', context)


@login_required
def mention_list(request):
    """List all mentions with filtering and pagination"""
    organization = get_current_organization(request)

    # Base queryset filtered by organization
    if organization:
        mentions = Mention.objects.filter(
            client__organization=organization
        ).select_related('client', 'created_by').order_by('-created_at')
    else:
        # Fallback if no organization context
        mentions = Mention.objects.all().select_related('client', 'created_by').order_by('-created_at')

    # Initialize filter form
    filter_form = MentionFilterForm(request.GET, organization=organization)

    if filter_form.is_valid():
        # Apply filters
        if filter_form.cleaned_data.get('status'):
            mentions = mentions.filter(status=filter_form.cleaned_data['status'])

        if filter_form.cleaned_data.get('priority'):
            mentions = mentions.filter(priority=filter_form.cleaned_data['priority'])

        if filter_form.cleaned_data.get('client'):
            mentions = mentions.filter(client=filter_form.cleaned_data['client'])

        if filter_form.cleaned_data.get('search'):
            search_term = filter_form.cleaned_data['search']
            mentions = mentions.filter(
                models.Q(title__icontains=search_term) |
                models.Q(content__icontains=search_term) |
                models.Q(client__name__icontains=search_term)
            )

    # Handle bulk actions
    if request.method == 'POST':
        bulk_form = BulkActionForm(request.POST)
        if bulk_form.is_valid():
            action = bulk_form.cleaned_data['action']
            selected_ids = bulk_form.cleaned_data['selected_mentions'].split(',')
            selected_mentions = mentions.filter(id__in=selected_ids)

            if action == 'approve':
                selected_mentions.update(
                    status='scheduled',
                    approved_by=request.user,
                    approved_at=timezone.now()
                )
                messages.success(request, f'Approved {selected_mentions.count()} mentions.')

            elif action == 'reject':
                selected_mentions.update(status='cancelled')
                messages.success(request, f'Rejected {selected_mentions.count()} mentions.')

            elif action == 'delete':
                count = selected_mentions.count()
                selected_mentions.delete()
                messages.success(request, f'Deleted {count} mentions.')

            elif action == 'change_priority':
                selected_mentions.update(priority=bulk_form.cleaned_data['new_priority'])
                messages.success(request, f'Updated priority for {selected_mentions.count()} mentions.')

            elif action == 'change_status':
                selected_mentions.update(status=bulk_form.cleaned_data['new_status'])
                messages.success(request, f'Updated status for {selected_mentions.count()} mentions.')

            return redirect('mentions:mention_list')

    # Pagination
    paginator = Paginator(mentions, 25)  # Show 25 mentions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get statistics
    stats = {
        'total': mentions.count(),
        'pending': mentions.filter(status='pending').count(),
        'scheduled': mentions.filter(status='scheduled').count(),
        'read': mentions.filter(status='read').count(),
        'cancelled': mentions.filter(status='cancelled').count(),
    }

    context = {
        'page_obj': page_obj,
        'filter_form': filter_form,
        'bulk_form': BulkActionForm(),
        'stats': stats,
        'organization': organization,
    }
    return render(request, 'mentions/mention_list.html', context)


@login_required
def mention_detail(request, pk):
    """Enhanced mention detail view with role-based context"""
    from apps.organizations.middleware import get_current_membership, user_has_permission
    from apps.core.models import Presenter

    mention = get_object_or_404(Mention, pk=pk)

    # Get current user context
    current_membership = get_current_membership(request)
    current_org = get_current_organization(request)

    # Get readings with related data
    readings = mention.mentionreading_set.select_related(
        'show', 'presenter', 'mention__client'
    ).order_by('scheduled_date', 'scheduled_time')

    # Calculate statistics
    completed_count = readings.filter(actual_read_time__isnull=False).count()
    pending_count = readings.filter(actual_read_time__isnull=True).count()

    # Filter readings based on user role
    user_readings = readings
    if current_membership and current_membership.role == 'presenter':
        # For presenters, show only their assigned readings
        try:
            presenter = Presenter.objects.get(
                user=request.user,
                organization=current_org,
                is_active=True
            )
            user_readings = readings.filter(presenter=presenter)
        except Presenter.DoesNotExist:
            user_readings = readings.none()

    # Check if user can manage this mention
    can_manage = user_has_permission(request, 'manage_mentions')
    can_schedule = user_has_permission(request, 'schedule_mentions')
    can_approve = user_has_permission(request, 'approve_mentions')

    # Get available shows for scheduling (if user can schedule)
    available_shows = []
    if can_schedule and current_org:
        from apps.shows.models import Show
        available_shows = Show.objects.filter(
            organization=current_org,
            is_active=True
        ).order_by('name')

    # Check for scheduling conflicts
    has_conflicts = False
    if readings.exists():
        for reading in readings:
            if reading.has_conflicts():
                has_conflicts = True
                break

    context = {
        'mention': mention,
        'readings': readings,
        'user_readings': user_readings,
        'current_membership': current_membership,
        'can_manage': can_manage,
        'can_schedule': can_schedule,
        'can_approve': can_approve,
        'available_shows': available_shows,
        'has_conflicts': has_conflicts,
        'completed_count': completed_count,
        'pending_count': pending_count,
    }

    return render(request, 'mentions/mention_detail.html', context)


@login_required
@require_permission('manage_mentions')
def mention_create(request):
    """Create new mention with optional immediate scheduling"""
    from .forms import MentionWithScheduleForm
    from apps.shows.models import Show
    from apps.core.models import Presenter
    from datetime import datetime, timedelta

    organization = get_current_organization(request)

    if request.method == 'POST':
        form = MentionWithScheduleForm(request.POST, organization=organization)
        if form.is_valid():
            mention = form.save(commit=False)
            mention.created_by = request.user
            mention.save()

            # Handle scheduling if requested
            if form.cleaned_data.get('schedule_immediately'):
                # Get scheduling data from POST
                show_ids = request.POST.getlist('shows')
                scheduled_dates = request.POST.getlist('scheduled_dates')
                scheduled_times = request.POST.getlist('scheduled_times')

                scheduled_count = 0
                validation_errors = []

                for i, show_id in enumerate(show_ids):
                    if i < len(scheduled_dates) and i < len(scheduled_times):
                        try:
                            show = Show.objects.get(id=show_id, organization=organization)
                            scheduled_date = datetime.strptime(scheduled_dates[i], '%Y-%m-%d').date()
                            scheduled_time = parse_time_string(scheduled_times[i])

                            # Validate time frame
                            time_errors = show.validate_mention_time(scheduled_date, scheduled_time)
                            if time_errors:
                                validation_errors.extend(time_errors)
                                continue

                            # Create the reading without presenter - will be assigned when read
                            reading = MentionReading.objects.create(
                                mention=mention,
                                show=show,
                                presenter=None,  # Will be assigned when actually read
                                scheduled_date=scheduled_date,
                                scheduled_time=scheduled_time
                            )
                            scheduled_count += 1

                        except (Show.DoesNotExist, ValueError) as e:
                            validation_errors.append(f"Invalid show or date/time format: {str(e)}")
                            continue

                # Handle validation errors
                if validation_errors:
                    for error in validation_errors:
                        messages.error(request, error)

                if scheduled_count > 0:
                    # Update mention status to scheduled
                    mention.status = 'scheduled'
                    mention.save()
                    messages.success(request, f'Mention created and scheduled to {scheduled_count} show(s)!')
                else:
                    messages.success(request, 'Mention created successfully! You can schedule it from the calendar.')
            else:
                messages.success(request, 'Mention created successfully!')

            return redirect('mentions:mention_detail', pk=mention.pk)
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = MentionWithScheduleForm(organization=organization)

    # Get shows for scheduling
    shows = Show.objects.filter(organization=organization, is_active=True).order_by('name')

    context = {
        'form': form,
        'organization': organization,
        'shows': shows,
    }
    return render(request, 'mentions/mention_form_with_schedule.html', context)


@login_required
@require_permission('manage_mentions')
def mention_edit(request, pk):
    """Edit mention"""
    organization = get_current_organization(request)
    mention = get_object_or_404(
        Mention,
        pk=pk,
        client__organization=organization
    )

    if request.method == 'POST':
        form = MentionForm(request.POST, instance=mention, organization=organization)
        if form.is_valid():
            form.save()
            messages.success(request, 'Mention updated successfully!')
            return redirect('mentions:mention_detail', pk=mention.pk)
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = MentionForm(instance=mention, organization=organization)

    context = {
        'form': form,
        'mention': mention,
        'organization': organization,
    }
    return render(request, 'mentions/mention_form.html', context)


@login_required
@require_permission('manage_mentions')
def mention_delete(request, pk):
    """Delete mention"""
    mention = get_object_or_404(Mention, pk=pk)
    if request.method == 'POST':
        mention.delete()
        messages.success(request, 'Mention deleted successfully!')
        return redirect('mentions:mention_list')
    return render(request, 'mentions/mention_confirm_delete.html', {'mention': mention})


@login_required
def approval_list(request):
    """List mentions pending approval"""
    organization = get_current_organization(request)

    if organization:
        pending_mentions = Mention.objects.filter(
            status='pending',
            client__organization=organization
        ).select_related('client')
    else:
        pending_mentions = Mention.objects.filter(status='pending').select_related('client')

    return render(request, 'mentions/approval_list.html', {
        'pending_mentions': pending_mentions,
        'organization': organization,
    })


@login_required
def approve_mention(request, pk):
    """Approve a mention"""
    if request.method == 'POST':
        try:
            organization = get_current_organization(request)

            # Try to find the mention with organization filter first, fallback if not found
            if organization:
                try:
                    mention = Mention.objects.get(pk=pk, client__organization=organization)
                except Mention.DoesNotExist:
                    # Fallback to finding mention without organization filter
                    # This handles cases where user might be viewing mentions from other organizations
                    mention = get_object_or_404(Mention, pk=pk)
            else:
                # No organization context, find mention without filter
                mention = get_object_or_404(Mention, pk=pk)

            mention.status = 'scheduled'
            mention.approved_by = request.user
            mention.approved_at = timezone.now()
            mention.save()

            return JsonResponse({'success': True, 'message': 'Mention approved successfully'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
def reject_mention(request, pk):
    """Reject a mention"""
    if request.method == 'POST':
        try:
            organization = get_current_organization(request)

            # Try to find the mention with organization filter first, fallback if not found
            if organization:
                try:
                    mention = Mention.objects.get(pk=pk, client__organization=organization)
                except Mention.DoesNotExist:
                    # Fallback to finding mention without organization filter
                    mention = get_object_or_404(Mention, pk=pk)
            else:
                # No organization context, find mention without filter
                mention = get_object_or_404(Mention, pk=pk)

            data = json.loads(request.body)
            reason = data.get('reason', '')

            mention.status = 'cancelled'
            if mention.notes:
                mention.notes = f"{mention.notes}\n\nRejected: {reason}"
            else:
                mention.notes = f"Rejected: {reason}"
            mention.save()

            return JsonResponse({'success': True, 'message': 'Mention rejected successfully'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
def reading_detail(request, pk):
    """Mention reading detail"""
    reading = get_object_or_404(MentionReading, pk=pk)

    if request.headers.get('Content-Type') == 'application/json' or request.GET.get('format') == 'json':
        # Return JSON for AJAX requests
        return JsonResponse({
            'success': True,
            'reading': {
                'id': reading.id,
                'mention': {
                    'title': reading.mention.title,
                    'content': reading.mention.content,
                    'client': {'name': reading.mention.client.name},
                    'duration_seconds': reading.mention.duration_seconds,
                    'priority_display': reading.mention.get_priority_display(),
                },
                'show': {'name': reading.show.name},
                'presenter': {'name': reading.presenter.display_name},
                'scheduled_date': reading.scheduled_date.strftime('%Y-%m-%d'),
                'scheduled_time': reading.scheduled_time.strftime('%H:%M'),
            }
        })

    return render(request, 'mentions/reading_detail.html', {'reading': reading})


@login_required
def reading_delete(request, pk):
    """Delete a mention reading"""
    reading = get_object_or_404(MentionReading, pk=pk)

    if request.method == 'POST':
        try:
            # Store mention for status update
            mention = reading.mention

            # Delete the reading
            reading.delete()

            # Check if this was the last reading for the mention
            remaining_readings = MentionReading.objects.filter(mention=mention).count()
            if remaining_readings == 0:
                # If no more readings, set mention back to pending
                mention.status = 'pending'
                mention.save()

            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
def schedule_mention(request):
    """Schedule a mention via AJAX"""
    if request.method == 'POST':
        data = json.loads(request.body)
        mention_id = data.get('mention_id')
        date = data.get('date')
        time = data.get('time')
        show_id = data.get('show_id')
        presenter_id = data.get('presenter_id')

        try:
            # Only allow scheduling of approved mentions that haven't been scheduled yet
            mention = Mention.objects.get(
                pk=mention_id,
                status='scheduled',
                mentionreading__isnull=True
            )

            # Get organization for filtering
            organization = get_current_organization(request)

            # If show_id and presenter_id are not provided, try to get defaults
            if not show_id or not presenter_id:
                # Get the first active show and presenter as defaults
                if not show_id:
                    if organization:
                        default_show = Show.objects.filter(organization=organization, is_active=True).first()
                    else:
                        default_show = Show.objects.filter(is_active=True).first()
                    show = default_show
                else:
                    if organization:
                        show = Show.objects.get(pk=show_id, organization=organization)
                    else:
                        show = Show.objects.get(pk=show_id)

                if not presenter_id:
                    from apps.core.models import Presenter
                    if organization:
                        default_presenter = Presenter.objects.filter(organization=organization, is_active=True).first()
                    else:
                        default_presenter = Presenter.objects.filter(is_active=True).first()
                    presenter = default_presenter
                else:
                    if organization:
                        presenter = Presenter.objects.get(pk=presenter_id, organization=organization)
                    else:
                        presenter = Presenter.objects.get(pk=presenter_id)
            else:
                if organization:
                    show = Show.objects.get(pk=show_id, organization=organization)
                    presenter = Presenter.objects.get(pk=presenter_id, organization=organization)
                else:
                    show = Show.objects.get(pk=show_id)
                    presenter = Presenter.objects.get(pk=presenter_id)

            if not show or not presenter:
                return JsonResponse({'success': False, 'error': 'Show and presenter are required'})

            # Parse time - handle HH:MM:SS or HH:MM format
            try:
                scheduled_time = parse_time_string(time)
            except ValueError:
                # Fallback: try to parse as hour only and add :00
                try:
                    hour = int(time)
                    scheduled_time = datetime.strptime(f'{hour:02d}:00', '%H:%M').time()
                except ValueError:
                    raise ValueError(f"Invalid time format: {time}. Expected HH:MM, HH:MM:SS, or hour format.")

            # Parse the date
            scheduled_date = datetime.strptime(date, '%Y-%m-%d').date()

            # Validate time frame
            time_errors = show.validate_mention_time(scheduled_date, scheduled_time)
            if time_errors:
                return JsonResponse({
                    'success': False,
                    'error': '; '.join(time_errors)
                })

            # Check for conflicts before creating the reading
            scheduled_datetime = datetime.combine(scheduled_date, scheduled_time)

            # Create a temporary reading to check for conflicts
            temp_reading = MentionReading(
                mention=mention,
                show=show,
                presenter=presenter,
                scheduled_date=scheduled_date,
                scheduled_time=scheduled_time,
            )

            # Check for conflicts using the model method
            if temp_reading.has_conflicts():
                return JsonResponse({
                    'success': False,
                    'error': 'This time slot conflicts with another mention. Please choose a different time.'
                })

            # Create the reading if no conflicts
            reading = MentionReading.objects.create(
                mention=mention,
                show=show,
                presenter=presenter,
                scheduled_date=scheduled_date,
                scheduled_time=scheduled_time,
            )

            # Note: Mention status remains 'scheduled' - no need to update it

            return JsonResponse({'success': True, 'reading_id': reading.pk})
        except Mention.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Mention not found or already scheduled'})
        except Show.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Show not found'})
        except Presenter.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Presenter not found'})
        except ValueError as e:
            return JsonResponse({'success': False, 'error': f'Invalid date/time format: {str(e)}'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': f'Error scheduling mention: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'Invalid request'})


@login_required
def mark_reading_complete(request, pk):
    """Mark a reading as completed"""
    if request.method == 'POST':
        try:
            reading = get_object_or_404(MentionReading, pk=pk)
            data = json.loads(request.body) if request.body else {}
            notes = data.get('notes', '')

            # Get current presenter if user is a presenter
            current_org = get_current_organization(request)
            try:
                presenter = Presenter.objects.get(
                    user=request.user,
                    organization=current_org,
                    is_active=True
                )
                reading.presenter = presenter  # Assign presenter when marking as read
                reading.read_by = presenter.display_name  # Update read_by field with presenter name
            except Presenter.DoesNotExist:
                # If not a presenter, leave presenter as None
                pass

            reading.actual_read_time = datetime.now()
            reading.notes = notes
            reading.save()

            # Update mention status if all readings are complete
            mention = reading.mention
            if not mention.mentionreading_set.filter(actual_read_time__isnull=True).exists():
                mention.status = 'read'
                mention.save()

            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request'})


@login_required
def cancel_mention(request, pk):
    """Cancel a mention"""
    if request.method == 'POST':
        try:
            mention = get_object_or_404(Mention, pk=pk)
            data = json.loads(request.body) if request.body else {}
            reason = data.get('reason', 'Cancelled from calendar interface')

            # Update mention status
            mention.status = 'cancelled'
            if mention.notes:
                mention.notes = f"{mention.notes}\n\nCancelled: {reason}"
            else:
                mention.notes = f"Cancelled: {reason}"
            mention.save()

            return JsonResponse({'success': True, 'message': 'Mention cancelled successfully'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
def calendar_interface(request):
    """Advanced calendar interface with drag and drop"""
    from apps.shows.models import Show
    from apps.core.models import Presenter
    from datetime import datetime, timedelta
    from calendar import monthrange

    # Get current organization
    organization = get_current_organization(request)

    # Get current date and view from request
    current_date = datetime.now().date()
    if request.GET.get('date'):
        try:
            current_date = datetime.strptime(request.GET.get('date'), '%Y-%m-%d').date()
        except ValueError:
            pass

    view = request.GET.get('view', 'day')
    group_by = request.GET.get('group_by', 'time')

    # Get all mention readings for the current date (filtered by organization)
    # This includes all statuses: pending, scheduled, completed, cancelled
    if organization:
        scheduled_readings = MentionReading.objects.filter(
            scheduled_date=current_date,
            mention__client__organization=organization
        ).select_related('mention', 'mention__client', 'show', 'presenter').order_by('scheduled_time')

        # Also get pending mentions that might be scheduled for today but don't have readings yet
        pending_mentions_today = Mention.objects.filter(
            status='pending',
            client__organization=organization,
            created_at__date=current_date
        ).select_related('client')
    else:
        scheduled_readings = MentionReading.objects.filter(
            scheduled_date=current_date
        ).select_related('mention', 'mention__client', 'show', 'presenter').order_by('scheduled_time')

        pending_mentions_today = Mention.objects.filter(
            status='pending',
            created_at__date=current_date
        ).select_related('client')

    # Group the readings based on the selected grouping option
    grouped_readings = _group_readings(scheduled_readings, group_by)

    # Get approved mentions that haven't been scheduled to specific time slots yet
    # These are mentions with status='scheduled' but no MentionReading records
    if organization:
        unscheduled_mentions = Mention.objects.filter(
            status='scheduled',
            mentionreading__isnull=True,
            client__organization=organization
        ).select_related('client')
    else:
        unscheduled_mentions = Mention.objects.filter(
            status='scheduled',
            mentionreading__isnull=True
        ).select_related('client')

    # Get active shows and presenters (filtered by organization)
    if organization:
        active_shows = Show.objects.filter(is_active=True, organization=organization)
        presenters = Presenter.objects.filter(is_active=True, organization=organization)
    else:
        active_shows = Show.objects.filter(is_active=True)
        presenters = Presenter.objects.filter(is_active=True)

    # Generate time slots (24 hours to show all mentions including late night/early morning)
    time_slots = list(range(0, 24))  # Midnight to 11 PM (24 hours)

    # Navigation dates
    if view == 'week':
        # Week navigation
        days_since_monday = current_date.weekday()
        week_start = current_date - timedelta(days=days_since_monday)
        prev_date = week_start - timedelta(days=7)
        next_date = week_start + timedelta(days=7)
    elif view == 'month':
        # Month navigation
        if current_date.month == 1:
            prev_date = current_date.replace(year=current_date.year - 1, month=12, day=1)
        else:
            prev_date = current_date.replace(month=current_date.month - 1, day=1)

        if current_date.month == 12:
            next_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
        else:
            next_date = current_date.replace(month=current_date.month + 1, day=1)
    else:
        # Day navigation
        prev_date = current_date - timedelta(days=1)
        next_date = current_date + timedelta(days=1)

    today = datetime.now().date()

    # Prepare view-specific data
    week_days = []
    month_weeks = []
    day_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']

    if view == 'week':
        # Generate week data
        days_since_monday = current_date.weekday()
        week_start = current_date - timedelta(days=days_since_monday)

        for i in range(7):
            day_date = week_start + timedelta(days=i)
            if organization:
                day_schedule = MentionReading.objects.filter(
                    scheduled_date=day_date,
                    mention__client__organization=organization
                ).select_related('mention', 'show', 'presenter', 'mention__client')
            else:
                day_schedule = MentionReading.objects.filter(
                    scheduled_date=day_date
                ).select_related('mention', 'show', 'presenter', 'mention__client')

            week_days.append({
                'date': day_date,
                'schedule': day_schedule,
                'is_today': day_date == today
            })

    elif view == 'month':
        # Generate month data
        year = current_date.year
        month = current_date.month

        # Get first day of month and number of days
        first_day = current_date.replace(day=1)
        days_in_month = monthrange(year, month)[1]

        # Calculate start of calendar (might be previous month)
        start_weekday = first_day.weekday()
        calendar_start = first_day - timedelta(days=start_weekday)

        # Generate 6 weeks of calendar
        for week in range(6):
            week_data = []
            for day in range(7):
                calendar_date = calendar_start + timedelta(days=week * 7 + day)

                # Get schedule for this day (filtered by organization)
                if organization:
                    day_schedule = MentionReading.objects.filter(
                        scheduled_date=calendar_date,
                        mention__client__organization=organization
                    ).select_related('mention', 'show', 'presenter', 'mention__client')[:3]  # Limit to 3 for display
                else:
                    day_schedule = MentionReading.objects.filter(
                        scheduled_date=calendar_date
                    ).select_related('mention', 'show', 'presenter', 'mention__client')[:3]  # Limit to 3 for display

                is_other_month = calendar_date.month != month
                is_today = calendar_date == today

                week_data.append({
                    'date': calendar_date,
                    'day': calendar_date.day,
                    'schedule': day_schedule,
                    'is_other_month': is_other_month,
                    'is_today': is_today
                })
            month_weeks.append(week_data)

    context = {
        'current_date': current_date,
        'prev_date': prev_date,
        'next_date': next_date,
        'today': today,
        'view': view,
        'group_by': group_by,
        'scheduled_readings': scheduled_readings,
        'grouped_readings': grouped_readings,
        'pending_mentions_today': pending_mentions_today,
        'unscheduled_mentions': unscheduled_mentions,
        'active_shows': active_shows,
        'presenters': presenters,
        'time_slots': time_slots,
        'week_days': week_days,
        'month_weeks': month_weeks,
        'day_names': day_names,
        'organization': organization,
    }
    return render(request, 'mentions/calendar_interface.html', context)


def _group_readings(readings, group_by):
    """Group readings based on the specified criteria"""
    from itertools import groupby
    from operator import attrgetter

    if group_by == 'client':
        # Group by client name
        sorted_readings = sorted(readings, key=lambda r: r.mention.client.name)
        return [(client, list(group)) for client, group in groupby(sorted_readings, key=lambda r: r.mention.client.name)]

    elif group_by == 'show':
        # Group by show name
        sorted_readings = sorted(readings, key=lambda r: r.show.name)
        return [(show, list(group)) for show, group in groupby(sorted_readings, key=lambda r: r.show.name)]

    elif group_by == 'presenter':
        # Group by presenter (handle None values)
        sorted_readings = sorted(readings, key=lambda r: r.presenter.name if r.presenter else 'Unassigned')
        return [(presenter, list(group)) for presenter, group in groupby(sorted_readings, key=lambda r: r.presenter.name if r.presenter else 'Unassigned')]

    elif group_by == 'priority':
        # Group by priority
        priority_names = {1: 'High Priority', 2: 'Medium Priority', 3: 'Low Priority'}
        sorted_readings = sorted(readings, key=lambda r: r.mention.priority)
        return [(priority_names.get(priority, f'Priority {priority}'), list(group)) for priority, group in groupby(sorted_readings, key=lambda r: r.mention.priority)]

    elif group_by == 'status':
        # Group by status
        def get_status(reading):
            if reading.actual_read_time:
                return 'Completed'
            elif reading.mention.status == 'pending':
                return 'Pending'
            elif reading.mention.status == 'cancelled':
                return 'Cancelled'
            else:
                return 'Scheduled'

        sorted_readings = sorted(readings, key=get_status)
        return [(status, list(group)) for status, group in groupby(sorted_readings, key=get_status)]

    else:  # Default to time grouping
        # Group by hour
        sorted_readings = sorted(readings, key=lambda r: r.scheduled_time.hour)
        return [(f"{hour:02d}:00", list(group)) for hour, group in groupby(sorted_readings, key=lambda r: r.scheduled_time.hour)]


@login_required
def approval_workflow(request):
    """Mention approval workflow"""
    organization = get_current_organization(request)

    # Base queryset with organization filtering (with fallback)
    if organization:
        base_queryset = Mention.objects.filter(client__organization=organization)
    else:
        base_queryset = Mention.objects.all()

    # Get mentions for each status
    pending_mentions = base_queryset.filter(
        status='pending'
    ).select_related('client', 'created_by').order_by('-created_at')

    # For "In Review" - we can use mentions that have been viewed but not yet approved/rejected
    # For now, let's use mentions that were recently updated but still pending
    in_review_mentions = base_queryset.filter(
        status='pending',
        updated_at__lt=timezone.now() - timedelta(hours=1)  # Older than 1 hour
    ).select_related('client', 'created_by').order_by('-updated_at')

    approved_mentions = base_queryset.filter(
        status='scheduled'
    ).select_related('client', 'created_by', 'approved_by').order_by('-approved_at')

    rejected_mentions = base_queryset.filter(
        status='cancelled'
    ).select_related('client', 'created_by').order_by('-updated_at')

    # Get counts for each status
    pending_count = pending_mentions.count()
    in_review_count = in_review_mentions.count()
    approved_count = approved_mentions.count()
    rejected_count = rejected_mentions.count()

    # Get all clients for filtering
    if organization:
        clients = Client.objects.filter(organization=organization, is_active=True)
    else:
        clients = Client.objects.filter(is_active=True)

    context = {
        'pending_count': pending_count,
        'in_review_count': in_review_count,
        'approved_count': approved_count,
        'rejected_count': rejected_count,
        'pending_mentions': pending_mentions,
        'in_review_mentions': in_review_mentions,
        'approved_mentions': approved_mentions,
        'rejected_mentions': rejected_mentions,
        'clients': clients,
        'organization': organization,
    }
    return render(request, 'mentions/approval_workflow.html', context)





@login_required
def recurring_mentions(request):
    """Recurring mentions management view"""
    from .models import RecurringMention
    from django.db.models import Q

    organization = get_current_organization(request)

    if organization:
        # Only show active and paused recurring mentions
        # Ended, finished, canceled, replaced, and split mentions should only appear in history
        recurring_mentions = RecurringMention.objects.filter(
            client__organization=organization,
            status__in=['active', 'paused']
        ).select_related('client', 'created_by').order_by('-created_at')
    else:
        # Fallback if no organization context
        recurring_mentions = RecurringMention.objects.filter(
            status__in=['active', 'paused']
        ).select_related('client', 'created_by').order_by('-created_at')

    # Initialize filter form
    filter_form = RecurringMentionFilterForm(request.GET, organization=organization)

    if filter_form.is_valid():
        # Apply filters
        if filter_form.cleaned_data.get('status'):
            recurring_mentions = recurring_mentions.filter(status=filter_form.cleaned_data['status'])

        if filter_form.cleaned_data.get('frequency'):
            recurring_mentions = recurring_mentions.filter(frequency=filter_form.cleaned_data['frequency'])

        if filter_form.cleaned_data.get('priority'):
            recurring_mentions = recurring_mentions.filter(priority=filter_form.cleaned_data['priority'])

        if filter_form.cleaned_data.get('client'):
            recurring_mentions = recurring_mentions.filter(client=filter_form.cleaned_data['client'])

        if filter_form.cleaned_data.get('search'):
            search_term = filter_form.cleaned_data['search']
            recurring_mentions = recurring_mentions.filter(
                Q(title__icontains=search_term) |
                Q(content__icontains=search_term) |
                Q(campaign_name__icontains=search_term) |
                Q(client__name__icontains=search_term)
            )

        if filter_form.cleaned_data.get('date_from'):
            recurring_mentions = recurring_mentions.filter(start_date__gte=filter_form.cleaned_data['date_from'])

        if filter_form.cleaned_data.get('date_to'):
            recurring_mentions = recurring_mentions.filter(start_date__lte=filter_form.cleaned_data['date_to'])

    # Get count of pending mentions from recurring patterns
    pending_mentions_count = 0
    if organization:
        pending_mentions_count = Mention.objects.filter(
            client__organization=organization,
            status='pending',
            recurring_mention__isnull=False
        ).count()
    else:
        pending_mentions_count = Mention.objects.filter(
            status='pending',
            recurring_mention__isnull=False
        ).count()

    # Calculate statistics properly (only active/paused patterns)
    total_patterns = recurring_mentions.count()
    daily_rotations = recurring_mentions.filter(frequency='daily').count()
    weekly_shows = recurring_mentions.filter(frequency='weekly').count()
    campaigns = recurring_mentions.filter(end_date__isnull=False).count()
    hourly_spots = recurring_mentions.filter(frequency='custom').count()

    # Calculate related mentions count
    from apps.mentions.models import MentionAuditLog
    related_mentions_count = MentionAuditLog.objects.filter(
        change_type='created',
        metadata__has_key='additional_mention_action'
    ).count() + MentionAuditLog.objects.filter(
        change_type='created',
        metadata__has_key='replacement_action'
    ).count()

    context = {
        'recurring_mentions': recurring_mentions,
        'organization': organization,
        'pending_mentions_count': pending_mentions_count,
        'filter_form': filter_form,
        'stats': {
            'total_patterns': total_patterns,
            'daily_rotations': daily_rotations,
            'weekly_shows': weekly_shows,
            'campaigns': campaigns,
            'hourly_spots': hourly_spots,
            'related_mentions': related_mentions_count,
        }
    }
    return render(request, 'mentions/recurring_mentions.html', context)


@login_required
@require_permission('manage_mentions')
def recurring_mention_create(request):
    """Redirect to new simplified wizard"""
    return redirect('mentions:recurring_wizard')




# ============================================================================
# NEW SIMPLIFIED RECURRING MENTION WIZARD
# ============================================================================

@login_required
@require_permission('manage_mentions')
def recurring_wizard(request):
    """Redirect to step 1 of the new simplified wizard"""
    return redirect('mentions:recurring_wizard_step1')


@login_required
@require_permission('manage_mentions')
def recurring_wizard_step(request, step):
    """Handle all steps of the simplified recurring mention wizard"""
    from apps.core.models import Client
    from apps.shows.models import Show
    from datetime import datetime, timedelta
    from dateutil.rrule import rrule, WEEKLY

    organization = get_current_organization(request)
    if not organization:
        messages.error(request, 'Organization context required.')
        return redirect('mentions:recurring_mentions')

    # Check for reset/start fresh request
    if request.GET.get('reset') == 'true':
        if 'simple_recurring_wizard' in request.session:
            del request.session['simple_recurring_wizard']
        messages.info(request, 'Wizard data cleared. Starting fresh.')
        return redirect('mentions:recurring_wizard_step', step=1)

    # Get or initialize wizard data with version checking
    wizard_data = _get_wizard_data(request)

    if step == 1:
        return _handle_wizard_step1(request, organization, wizard_data)
    elif step == 2:
        return _handle_wizard_step2(request, organization, wizard_data)
    elif step == 3:
        return _handle_wizard_step3(request, organization, wizard_data)
    else:
        messages.error(request, 'Invalid step.')
        return redirect('mentions:recurring_wizard_step', step=1)


def _handle_wizard_step1(request, organization, wizard_data):
    """Step 1: Basic mention info and days selection"""
    from apps.core.models import Client

    # Check for start fresh request in POST
    if request.method == 'POST' and request.POST.get('start_fresh') == 'true':
        if 'simple_recurring_wizard' in request.session:
            del request.session['simple_recurring_wizard']
        messages.info(request, 'Starting fresh with a new recurring mention.')
        return redirect('mentions:recurring_wizard_step', step=1)

    if request.method == 'POST':
        # Validate and save step 1 data
        title = request.POST.get('title', '').strip()
        content = request.POST.get('content', '').strip()
        client_id = request.POST.get('client_id')
        selected_days = request.POST.getlist('selected_days')

        if not all([title, content, client_id, selected_days]):
            messages.error(request, 'Please fill in all required fields.')
        else:
            wizard_data.update({
                'title': title,
                'content': content,
                'client_id': int(client_id),
                'duration_seconds': int(request.POST.get('duration_seconds', 30)),
                'priority': int(request.POST.get('priority', 2)),
                'selected_days': [int(day) for day in selected_days]
            })
            request.session['simple_recurring_wizard'] = wizard_data
            return redirect('mentions:recurring_wizard_step', step=2)

    clients = Client.objects.filter(organization=organization, is_active=True)

    context = {
        'step': 1,
        'wizard_data': wizard_data,
        'clients': clients,
        'weekdays': [
            (0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'),
            (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')
        ]
    }
    return render(request, 'mentions/simple_wizard.html', context)


def _handle_wizard_step2(request, organization, wizard_data):
    """Step 2: Schedule shows and times with comprehensive validation"""
    from apps.shows.models import Show
    from datetime import datetime, time

    if not wizard_data.get('selected_days'):
        messages.error(request, 'Please complete Step 1 first.')
        return redirect('mentions:recurring_wizard_step', step=1)

    if request.method == 'POST':
        # Process schedule assignments with validation
        schedule = {}
        validation_errors = []
        duplicate_check = set()  # Track duplicates within this submission

        for day in wizard_data['selected_days']:
            day_schedule = []
            show_ids = request.POST.getlist(f'day_{day}_shows')
            times = request.POST.getlist(f'day_{day}_times')

            weekday_name = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][day]

            for i, (show_id, time_str) in enumerate(zip(show_ids, times)):
                if not show_id or not time_str:
                    continue  # Skip empty entries

                try:
                    # Validate show exists and belongs to organization
                    show = Show.objects.get(id=int(show_id), organization=organization, is_active=True)
                except (Show.DoesNotExist, ValueError):
                    validation_errors.append(f'{weekday_name}: Invalid show selected (slot {i+1})')
                    continue

                try:
                    # Validate time format using helper function
                    scheduled_time = parse_time_string(time_str)
                except ValueError:
                    validation_errors.append(f'{weekday_name}: Invalid time format "{time_str}" (slot {i+1}). Use HH:MM or HH:MM:SS format.')
                    continue

                # Check if show airs on this day (strict validation)
                show_airs_on_day = True
                if hasattr(show, 'days_of_week') and show.days_of_week:
                    if isinstance(show.days_of_week, list) and len(show.days_of_week) > 0:
                        show_airs_on_day = day in show.days_of_week

                if not show_airs_on_day:
                    validation_errors.append(f'{weekday_name}: Show "{show.name}" is not configured to air on {weekday_name}s and cannot be selected')
                    continue

                # Validate time is within show's time frame
                if hasattr(show, 'start_time') and hasattr(show, 'end_time'):
                    if show.start_time and show.end_time:
                        if not (show.start_time <= scheduled_time <= show.end_time):
                            validation_errors.append(
                                f'{weekday_name}: Time {scheduled_time.strftime("%H:%M")} is outside '
                                f'show "{show.name}" time frame ({show.start_time.strftime("%H:%M")} - {show.end_time.strftime("%H:%M")})'
                            )
                            continue

                # Check for duplicates within this submission (same day only)
                duplicate_key = (day, show.id, scheduled_time)  # Include day in the key
                if duplicate_key in duplicate_check:
                    validation_errors.append(f'{weekday_name}: Duplicate assignment - Show "{show.name}" at {scheduled_time.strftime("%H:%M")} already scheduled on {weekday_name}')
                    continue
                duplicate_check.add(duplicate_key)

                # Check for conflicts with existing recurring mentions (optional - can be disabled)
                check_conflicts = request.POST.get('check_conflicts', 'true') == 'true'
                if check_conflicts:
                    client_id = wizard_data.get('client_id')
                    existing_conflicts = _check_recurring_conflicts(show, scheduled_time, day, organization, client_id=client_id)
                    if existing_conflicts:
                        for conflict in existing_conflicts:
                            validation_errors.append(f'{weekday_name}: Conflict with existing recurring mention "{conflict}" for show "{show.name}" at {scheduled_time.strftime("%H:%M")}')

                # If all validations pass, add to schedule
                day_schedule.append({
                    'show_id': show.id,
                    'time': time_str,
                    'show_name': show.name  # Store for display
                })

            if day_schedule:
                schedule[day] = day_schedule

        # Final validations
        if not schedule:
            validation_errors.append('Please schedule at least one show.')

        # Check minimum requirements
        total_slots = sum(len(day_schedule) for day_schedule in schedule.values())
        if total_slots < 1:
            validation_errors.append('At least one time slot must be scheduled.')

        # Display validation errors
        if validation_errors:
            for error in validation_errors:
                messages.error(request, error)
        else:
            # Save valid schedule
            wizard_data['schedule'] = schedule
            wizard_data['total_slots'] = total_slots
            request.session['simple_recurring_wizard'] = wizard_data
            messages.success(request, f'Schedule configured successfully! {total_slots} time slot(s) per week.')
            return redirect('mentions:recurring_wizard_step', step=3)

    shows = Show.objects.filter(organization=organization, is_active=True)
    weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    # Prepare day data with show filtering and validation info
    selected_days_data = []
    for day in wizard_data['selected_days']:
        # Filter shows that can air on this day
        available_shows = []
        unavailable_shows = []

        for show in shows:
            show_airs_on_day = True
            if hasattr(show, 'days_of_week') and show.days_of_week:
                if isinstance(show.days_of_week, list) and len(show.days_of_week) > 0:
                    show_airs_on_day = day in show.days_of_week

            if show_airs_on_day:
                available_shows.append(show)
            else:
                unavailable_shows.append(show)

        selected_days_data.append({
            'number': day,
            'name': weekday_names[day],
            'shows': shows,  # All shows for dropdown
            'available_shows': available_shows,
            'unavailable_shows': unavailable_shows,
            'has_available_shows': len(available_shows) > 0
        })

    # Get organization settings for overlapping configuration
    try:
        org_settings = organization.settings
        allow_overlapping = org_settings.allow_overlapping_mentions
    except:
        allow_overlapping = False

    context = {
        'step': 2,
        'wizard_data': wizard_data,
        'selected_days_data': selected_days_data,
        'shows': shows,
        'allow_overlapping_mentions': allow_overlapping,
    }
    return render(request, 'mentions/simple_wizard.html', context)


def _check_recurring_conflicts(show, scheduled_time, weekday, organization, exclude_recurring_mention_id=None, client_id=None):
    """Check for conflicts with existing recurring mentions on the same weekday with caching"""
    from .models import RecurringMention, RecurringMentionShow
    from datetime import datetime, date, timedelta
    from django.core.cache import cache
    import hashlib

    # Create cache key based on parameters
    cache_key_data = f"recurring_conflicts_{show.id}_{scheduled_time}_{weekday}_{organization.id}_{exclude_recurring_mention_id or 'none'}_{client_id or 'none'}"
    cache_key = hashlib.md5(cache_key_data.encode()).hexdigest()

    # Try to get from cache first (cache for 5 minutes)
    cached_result = cache.get(cache_key)
    if cached_result is not None:
        return cached_result

    conflicts = []

    # Check if overlapping mentions are allowed in organization settings
    try:
        settings = organization.settings
        if settings.allow_overlapping_mentions:
            # Skip conflict checking if overlapping mentions are allowed
            return conflicts
    except:
        # If we can't get settings, proceed with conflict detection as default
        pass

    # Check for existing recurring mentions that might conflict on the same weekday
    existing_assignments = RecurringMentionShow.objects.filter(
        show=show,
        scheduled_time=scheduled_time,
        recurring_mention__client__organization=organization,
        recurring_mention__is_active=True
    ).select_related('recurring_mention', 'recurring_mention__client')

    # Exclude the current recurring mention being edited (if any)
    if exclude_recurring_mention_id:
        existing_assignments = existing_assignments.exclude(recurring_mention_id=exclude_recurring_mention_id)

    today = date.today()
    # Allow some overlap buffer (e.g., 30 days in the future)
    future_buffer = today + timedelta(days=30)

    for assignment in existing_assignments:
        recurring_mention = assignment.recurring_mention

        # Skip if this recurring mention has ended (past end_date)
        if hasattr(recurring_mention, 'end_date') and recurring_mention.end_date:
            if recurring_mention.end_date < today:
                continue

        # Skip if this recurring mention starts too far in the future
        if hasattr(recurring_mention, 'start_date') and recurring_mention.start_date:
            if recurring_mention.start_date > future_buffer:
                continue

        # Allow different clients to use the same show/time (less restrictive)
        if client_id and hasattr(recurring_mention, 'client') and recurring_mention.client:
            if recurring_mention.client.id != client_id:
                # Different client - allow overlap but show as warning instead of error
                continue

        # Check if this recurring mention includes the same weekday
        if hasattr(recurring_mention, 'weekdays') and recurring_mention.weekdays:
            if weekday in recurring_mention.weekdays:
                client_info = f" (Client: {recurring_mention.client.name})" if recurring_mention.client else ""
                conflicts.append(f"{recurring_mention.title}{client_info} (already scheduled for {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][weekday]}s)")

    # Cache the result for 5 minutes
    cache.set(cache_key, conflicts, 300)  # Cache for 5 minutes
    return conflicts


def _handle_wizard_step3(request, organization, wizard_data):
    """Step 3: Set period and preview with enhanced functionality"""
    from apps.core.models import Client
    from apps.shows.models import Show
    from datetime import datetime, timedelta
    from dateutil.rrule import rrule, WEEKLY

    if not wizard_data.get('schedule'):
        messages.error(request, 'Session expired or wizard data lost. Please start over from Step 1.')
        return redirect('mentions:recurring_wizard_step', step=1)

    if request.method == 'POST':
        start_date = request.POST.get('start_date')
        weeks = request.POST.get('weeks')
        end_date = request.POST.get('end_date')
        preview_only = request.POST.get('preview_only') == 'true'

        # Validation
        validation_errors = []

        if not start_date:
            validation_errors.append('Start date is required.')
        else:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                if start_date_obj < datetime.now().date():
                    validation_errors.append('Start date cannot be in the past.')
            except ValueError:
                validation_errors.append('Invalid start date format.')

        # Handle duration input (either weeks or end date)
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                if end_date_obj <= start_date_obj:
                    validation_errors.append('End date must be after start date.')
                else:
                    # Calculate weeks from date range
                    weeks = max(1, (end_date_obj - start_date_obj).days // 7)
            except ValueError:
                validation_errors.append('Invalid end date format.')
        elif weeks:
            try:
                weeks = int(weeks)
                if weeks < 1 or weeks > 52:
                    validation_errors.append('Duration must be between 1 and 52 weeks.')
            except ValueError:
                validation_errors.append('Invalid duration format.')
        else:
            validation_errors.append('Please specify either duration in weeks or end date.')

        # Handle AJAX preview requests
        if preview_only and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            from django.http import JsonResponse
            from django.template.loader import render_to_string

            if validation_errors:
                return JsonResponse({
                    'success': False,
                    'errors': validation_errors
                })

            # Generate preview data with new parameters
            preview_data, statistics = _generate_preview_data(
                organization, wizard_data, start_date, int(weeks)
            )

            # Render preview HTML
            preview_html = render_to_string('mentions/wizard_preview_partial.html', {
                'preview_data': preview_data,
                'statistics': statistics,
            })

            return JsonResponse({
                'success': True,
                'preview_html': preview_html,
                'statistics': statistics
            })

        if validation_errors:
            for error in validation_errors:
                messages.error(request, error)
        else:
            wizard_data.update({
                'start_date': start_date,
                'weeks': int(weeks),
                'end_date': end_date if end_date else None
            })
            request.session['simple_recurring_wizard'] = wizard_data
            return redirect('mentions:recurring_wizard_save')

    # Generate enhanced preview
    client = Client.objects.get(id=wizard_data['client_id'])

    # Use provided start date or default to today
    start_date_str = wizard_data.get('start_date')
    if not start_date_str:
        # Set default start date to today and save it in wizard_data
        start_date_str = datetime.now().date().strftime('%Y-%m-%d')
        wizard_data['start_date'] = start_date_str
        request.session['simple_recurring_wizard'] = wizard_data

    weeks_count = wizard_data.get('weeks', 1)

    # Get end_date from wizard_data or calculate from weeks for backward compatibility
    end_date_str = wizard_data.get('end_date')
    if not end_date_str and weeks_count:
        # Calculate end_date from weeks for backward compatibility
        start_date_obj = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date_obj = start_date_obj + timedelta(weeks=weeks_count)
        end_date_str = end_date_obj.strftime('%Y-%m-%d')

    preview_data, statistics = _generate_preview_data(
        organization, wizard_data, start_date_str, end_date_str
    )

    context = {
        'step': 3,
        'wizard_data': wizard_data,
        'client': client,
        'preview_data': preview_data,
        'total_mentions_preview': statistics['total_mentions_preview'],
        'estimated_total_mentions': statistics['estimated_total_mentions'],
        'mentions_per_week': statistics['mentions_per_week'],
        'total_days': statistics['total_days'],
        'duration_text': statistics['duration_text'],
        'cost_estimate': statistics['cost_estimate'],
        'preview_days': statistics['preview_days'],
        'today': datetime.now().date(),
    }
    return render(request, 'mentions/simple_wizard.html', context)


def _generate_preview_data(organization, wizard_data, start_date_str, end_date_str):
    """Generate preview data and statistics for the wizard using day-by-day approach"""
    from apps.core.models import Client
    from apps.shows.models import Show
    from datetime import datetime, timedelta

    client = Client.objects.get(id=wizard_data['client_id'])
    shows_dict = {show.id: show for show in Show.objects.filter(organization=organization)}

    # Parse dates with better error handling
    try:
        if not start_date_str or not end_date_str:
            logger.error(f"Missing date parameters: start={start_date_str}, end={end_date_str}")
            return [], {
                'total_mentions_preview': 0,
                'estimated_total_mentions': 0,
                'mentions_per_week': 0,
                'total_weeks': 0,
                'total_days': 0,
                'cost_estimate': None,
                'preview_days': 0,
            }

        # Handle case where end_date_str might be an integer (backward compatibility)
        if isinstance(end_date_str, int):
            # This is likely weeks_count passed as second parameter
            weeks_count = end_date_str
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = start_date + timedelta(weeks=weeks_count)
            logger.warning(f"Received integer as end_date_str ({weeks_count}), treating as weeks_count for backward compatibility")
        else:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

    except (ValueError, TypeError) as e:
        logger.error(f"Invalid date format: start={start_date_str}, end={end_date_str}, error={e}")
        return [], {
            'total_mentions_preview': 0,
            'estimated_total_mentions': 0,
            'mentions_per_week': 0,
            'total_weeks': 0,
            'total_days': 0,
            'cost_estimate': None,
            'preview_days': 0,
        }

    # Ensure we have valid data to work with
    if not wizard_data.get('selected_days') or not wizard_data.get('schedule'):
        return [], {
            'total_mentions_preview': 0,
            'estimated_total_mentions': 0,
            'mentions_per_week': 0,
            'total_weeks': 0,
            'total_days': 0,
            'cost_estimate': None,
            'preview_days': 0,
        }

    # Calculate total days - show all dates in preview
    total_days = (end_date - start_date).days + 1
    preview_days = total_days  # Show all days in preview

    # Debug logging
    logger.info(f"📅 Duration calculation debug:")
    logger.info(f"  Start date: {start_date}")
    logger.info(f"  End date: {end_date}")
    logger.info(f"  Total days: {total_days}")
    logger.info(f"  Preview days: {preview_days}")

    preview_data = []
    total_mentions_preview = 0
    total_mentions_full = 0
    weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    # Generate preview data day by day
    current_date = start_date
    current_week = None
    week_data = None

    # Generate preview data for all days in the range
    while current_date <= end_date:

        current_weekday = current_date.weekday()

        # Check if this weekday has scheduled slots
        day_schedule = wizard_data['schedule'].get(str(current_weekday), [])
        if not day_schedule:
            day_schedule = wizard_data['schedule'].get(current_weekday, [])

        # Group by weeks for display
        week_number = ((current_date - start_date).days // 7) + 1
        if current_week != week_number:
            if week_data and week_data['dates']:
                preview_data.append(week_data)
            current_week = week_number
            week_data = {'week': week_number, 'dates': [], 'week_start': current_date}

        # Add mentions for this day
        for item in day_schedule:
            show = shows_dict.get(item['show_id'])
            times = item.get('times', []) if isinstance(item.get('times'), list) else [item.get('time')]

            for time in times:
                if show and time:
                    week_data['dates'].append({
                        'date': current_date,
                        'weekday': weekday_names[current_weekday],
                        'show': show.name,
                        'show_id': show.id,
                        'time': time,
                        'duration': wizard_data.get('duration_seconds', 30)
                    })
                    total_mentions_preview += 1

        current_date += timedelta(days=1)

    # Add the last week if it has data
    if week_data and week_data['dates']:
        preview_data.append(week_data)

    # Sort each week's dates by date and time
    for week in preview_data:
        week['dates'].sort(key=lambda x: (x['date'], x['time']))

    # Simple calculation: count slots per day and multiply by total days
    slots_per_day = {}
    for day_num, day_schedule in wizard_data['schedule'].items():
        day_key = int(day_num) if isinstance(day_num, str) else day_num
        slot_count = 0
        for item in day_schedule:
            times = item.get('times', []) if isinstance(item.get('times'), list) else [item.get('time')]
            slot_count += len([t for t in times if t])
        slots_per_day[day_key] = slot_count

    # Count total mentions by going through each day in the range
    total_mentions_full = 0
    current_date = start_date
    while current_date <= end_date:
        weekday = current_date.weekday()
        if weekday in slots_per_day:
            total_mentions_full += slots_per_day[weekday]
        current_date += timedelta(days=1)

    # Calculate duration in weeks and days
    weeks = total_days // 7
    remaining_days = total_days % 7

    logger.info(f"📊 Duration breakdown:")
    logger.info(f"  Total days: {total_days}")
    logger.info(f"  Weeks: {weeks}")
    logger.info(f"  Remaining days: {remaining_days}")

    if weeks > 0 and remaining_days > 0:
        duration_text = f"{weeks} week{'s' if weeks != 1 else ''} and {remaining_days} day{'s' if remaining_days != 1 else ''}"
    elif weeks > 0:
        duration_text = f"{weeks} week{'s' if weeks != 1 else ''}"
    else:
        duration_text = f"{remaining_days} day{'s' if remaining_days != 1 else ''}"

    logger.info(f"  Final duration text: '{duration_text}'")

    # Calculate cost estimate if client has rate information
    cost_estimate = None
    if hasattr(client, 'rate_per_mention') and client.rate_per_mention:
        cost_estimate = total_mentions_full * client.rate_per_mention

    statistics = {
        'total_mentions_preview': total_mentions_preview,
        'estimated_total_mentions': total_mentions_full,
        'mentions_per_week': round(total_mentions_full / max(1, total_days / 7)) if total_days >= 7 else total_mentions_full,
        'total_days': total_days,
        'duration_text': duration_text,
        'cost_estimate': cost_estimate,
        'preview_days': preview_days,
    }

    return preview_data, statistics


def _generate_mentions_from_preview(organization, wizard_data, start_date_str, end_date_str, recurring_mention):
    """
    Generate actual mentions using the same logic as the preview.
    This ensures consistency between what the user sees and what gets created.
    """
    from apps.shows.models import Show
    from apps.mentions.models import Mention, MentionReading
    from datetime import datetime, timedelta

    logger.info(f"Generating mentions using preview logic for RecurringMention {recurring_mention.id}")

    # Parse start and end dates
    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    except ValueError as e:
        logger.error(f"Invalid date format: start={start_date_str}, end={end_date_str}, error={e}")
        return []

    # Get shows for lookup
    shows = Show.objects.filter(organization=organization, is_active=True)
    shows_dict = {show.id: show for show in shows}

    weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    generated_mentions = []
    total_mentions = 0

    logger.info(f"Generating mentions from {start_date} to {end_date}")
    logger.info(f"Schedule data: {wizard_data.get('schedule', {})}")
    logger.info(f"Selected days: {wizard_data.get('selected_days', [])}")

    # Generate mentions day by day (same logic as preview)
    current_date = start_date
    while current_date <= end_date:
        current_weekday = current_date.weekday()

        # Check if this weekday has scheduled slots
        day_schedule = wizard_data['schedule'].get(str(current_weekday), [])
        if not day_schedule:
            day_schedule = wizard_data['schedule'].get(current_weekday, [])

        logger.debug(f"  {weekday_names[current_weekday]} ({current_date}): {len(day_schedule)} slots")

        # Process each scheduled slot for this day
        for item in day_schedule:
            show = shows_dict.get(item['show_id'])
            times = item.get('times', []) if isinstance(item.get('times'), list) else [item.get('time')]

            for time_str in times:
                if show and time_str:
                    try:
                        # Parse the time
                        scheduled_time = parse_time_string(time_str)

                        # Create the mention
                        mention = Mention(
                            title=recurring_mention.title,
                            content=recurring_mention.content,
                            client=recurring_mention.client,
                            priority=recurring_mention.priority,
                            duration_seconds=recurring_mention.duration_seconds,
                            status='pending',
                            created_by=recurring_mention.created_by,
                            recurring_mention=recurring_mention
                        )

                        # Store the reading data temporarily
                        mention._temp_reading = {
                            'show': show,
                            'scheduled_date': current_date,
                            'scheduled_time': scheduled_time
                        }

                        generated_mentions.append(mention)
                        total_mentions += 1

                        logger.debug(f"    Created mention: {show.name} at {time_str} on {current_date}")

                    except ValueError as e:
                        logger.error(f"Error parsing time '{time_str}': {e}")
                        continue
                    except Exception as e:
                        logger.error(f"Error creating mention for {show.name} at {time_str} on {current_date}: {e}")
                        continue

        current_date += timedelta(days=1)

    # Bulk create mentions
    if generated_mentions:
        logger.info(f"Bulk creating {len(generated_mentions)} mentions")
        Mention.objects.bulk_create(generated_mentions)

        # Create readings
        readings_to_create = []
        for mention in generated_mentions:
            if hasattr(mention, '_temp_reading'):
                reading_data = mention._temp_reading
                reading = MentionReading(
                    mention=mention,
                    show=reading_data['show'],
                    presenter=None,
                    scheduled_date=reading_data['scheduled_date'],
                    scheduled_time=reading_data['scheduled_time']
                )
                readings_to_create.append(reading)

        if readings_to_create:
            logger.info(f"Bulk creating {len(readings_to_create)} mention readings")
            MentionReading.objects.bulk_create(readings_to_create)

    logger.info(f"Successfully generated {len(generated_mentions)} mentions using preview logic")
    return generated_mentions


@login_required
@require_permission('manage_mentions')
def recurring_wizard_clear(request):
    """Clear wizard data and start fresh"""
    if 'simple_recurring_wizard' in request.session:
        del request.session['simple_recurring_wizard']
    messages.success(request, 'Wizard data cleared. You can now start fresh.')
    return redirect('mentions:recurring_wizard_step1')


# ============================================================================
# NEW SEPARATE PAGE WIZARD VIEWS
# ============================================================================

@login_required
@require_permission('manage_mentions')
def recurring_wizard_step1(request):
    """Step 1: Basic mention info and days selection - Separate Page"""
    from apps.core.models import Client

    organization = get_current_organization(request)
    if not organization:
        messages.error(request, 'Organization context required.')
        return redirect('mentions:recurring_mentions')

    # Check for reset/start fresh request
    if request.GET.get('reset') == 'true':
        if 'simple_recurring_wizard' in request.session:
            del request.session['simple_recurring_wizard']
        messages.info(request, 'Wizard data cleared. Starting fresh.')
        return redirect('mentions:recurring_wizard_step1')

    # Get or initialize wizard data
    wizard_data = _get_wizard_data(request)

    # Check for start fresh request in POST
    if request.method == 'POST' and request.POST.get('start_fresh') == 'true':
        if 'simple_recurring_wizard' in request.session:
            del request.session['simple_recurring_wizard']
        messages.info(request, 'Starting fresh with a new recurring mention.')
        return redirect('mentions:recurring_wizard_step1')

    if request.method == 'POST':
        # Validate and save step 1 data
        title = request.POST.get('title', '').strip()
        content = request.POST.get('content', '').strip()
        client_id = request.POST.get('client_id')
        selected_days = request.POST.getlist('selected_days')

        if not all([title, content, client_id, selected_days]):
            messages.error(request, 'Please fill in all required fields.')
        else:
            wizard_data.update({
                'title': title,
                'content': content,
                'client_id': int(client_id),
                'duration_seconds': int(request.POST.get('duration_seconds', 30)),
                'priority': int(request.POST.get('priority', 2)),
                'selected_days': [int(day) for day in selected_days]
            })
            request.session['simple_recurring_wizard'] = wizard_data

            # Force session save to ensure data persists
            request.session.modified = True
            request.session.save()

            messages.success(request, 'Basic information saved. Now configure the schedule.')
            return redirect('mentions:recurring_wizard_step2')

    clients = Client.objects.filter(organization=organization, is_active=True)

    context = {
        'wizard_data': wizard_data,
        'clients': clients,
        'weekdays': [
            (0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'),
            (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')
        ]
    }
    return render(request, 'mentions/wizard_step1.html', context)


@login_required
@require_permission('manage_mentions')
def recurring_wizard_step2(request):
    """Step 2: Schedule shows and times - Separate Page"""
    from apps.shows.models import Show
    from datetime import datetime, time

    organization = get_current_organization(request)
    if not organization:
        messages.error(request, 'Organization context required.')
        return redirect('mentions:recurring_mentions')

    # Get wizard data and validate step 1 completion
    wizard_data = _get_wizard_data(request)
    if not wizard_data.get('selected_days'):
        messages.error(request, 'Please complete Step 1 first.')
        return redirect('mentions:recurring_wizard_step1')

    if request.method == 'POST':
        # Process schedule assignments with validation
        schedule = {}
        validation_errors = []
        duplicate_check = set()  # Track duplicates within this submission

        for day in wizard_data['selected_days']:
            day_schedule = []
            show_ids = request.POST.getlist(f'day_{day}_shows')
            times = request.POST.getlist(f'day_{day}_times')

            weekday_name = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][day]

            for i, (show_id, time_str) in enumerate(zip(show_ids, times)):
                if not show_id or not time_str:
                    continue  # Skip empty entries

                try:
                    # Validate show exists and belongs to organization
                    show = Show.objects.get(id=int(show_id), organization=organization, is_active=True)
                except (Show.DoesNotExist, ValueError):
                    validation_errors.append(f'{weekday_name}: Invalid show selected (slot {i+1})')
                    continue

                try:
                    # Validate time format using helper function
                    scheduled_time = parse_time_string(time_str)
                except ValueError:
                    validation_errors.append(f'{weekday_name}: Invalid time format "{time_str}" (slot {i+1}). Use HH:MM or HH:MM:SS format.')
                    continue

                # Check if show airs on this day (strict validation)
                show_airs_on_day = True
                if hasattr(show, 'days_of_week') and show.days_of_week:
                    if isinstance(show.days_of_week, list) and len(show.days_of_week) > 0:
                        show_airs_on_day = day in show.days_of_week

                if not show_airs_on_day:
                    validation_errors.append(f'{weekday_name}: Show "{show.name}" is not configured to air on {weekday_name}s and cannot be selected')
                    continue

                # Validate time is within show's time frame
                if hasattr(show, 'start_time') and hasattr(show, 'end_time'):
                    if show.start_time and show.end_time:
                        if not (show.start_time <= scheduled_time <= show.end_time):
                            validation_errors.append(
                                f'{weekday_name}: Time {scheduled_time.strftime("%H:%M")} is outside '
                                f'show "{show.name}" time frame ({show.start_time.strftime("%H:%M")} - {show.end_time.strftime("%H:%M")})'
                            )
                            continue

                # Check for duplicates within this submission (same day only)
                duplicate_key = (day, show.id, scheduled_time)  # Include day in the key
                if duplicate_key in duplicate_check:
                    validation_errors.append(f'{weekday_name}: Duplicate assignment - Show "{show.name}" at {scheduled_time.strftime("%H:%M")} already scheduled on {weekday_name}')
                    continue
                duplicate_check.add(duplicate_key)

                # Check for conflicts with existing recurring mentions (optional - can be disabled)
                check_conflicts = request.POST.get('check_conflicts', 'true') == 'true'
                if check_conflicts:
                    client_id = wizard_data.get('client_id')
                    existing_conflicts = _check_recurring_conflicts(show, scheduled_time, day, organization, client_id=client_id)
                    if existing_conflicts:
                        for conflict in existing_conflicts:
                            validation_errors.append(f'{weekday_name}: Conflict with existing recurring mention "{conflict}" for show "{show.name}" at {scheduled_time.strftime("%H:%M")}')

                # If all validations pass, add to schedule
                day_schedule.append({
                    'show_id': show.id,
                    'time': time_str,
                    'show_name': show.name  # Store for display
                })

            if day_schedule:
                schedule[day] = day_schedule

        # Final validations
        if not schedule:
            validation_errors.append('Please schedule at least one show.')

        # Check minimum requirements
        total_slots = sum(len(day_schedule) for day_schedule in schedule.values())

        # Debug logging for slot calculation
        logger.info(f"Step 2 slot calculation debug:")
        logger.info(f"  Schedule data: {schedule}")
        logger.info(f"  Day schedule lengths: {[len(day_schedule) for day_schedule in schedule.values()]}")
        logger.info(f"  Total slots calculated: {total_slots}")

        if total_slots < 1:
            validation_errors.append('At least one time slot must be scheduled.')

        # Display validation errors
        if validation_errors:
            for error in validation_errors:
                messages.error(request, error)
        else:
            # Save valid schedule
            wizard_data['schedule'] = schedule
            wizard_data['total_slots'] = total_slots
            request.session['simple_recurring_wizard'] = wizard_data

            # Force session save to ensure data persists
            request.session.modified = True
            request.session.save()

            logger.info(f"Step 2 success: Saved {total_slots} slots to session")
            messages.success(request, f'Schedule configured successfully! {total_slots} time slot(s) per week.')
            return redirect('mentions:recurring_wizard_step3')

    shows = Show.objects.filter(organization=organization, is_active=True)
    weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    # Prepare day data with show filtering and validation info
    selected_days_data = []
    for day in wizard_data['selected_days']:
        # Filter shows that can air on this day
        available_shows = []
        unavailable_shows = []

        for show in shows:
            show_airs_on_day = True
            if hasattr(show, 'days_of_week') and show.days_of_week:
                if isinstance(show.days_of_week, list) and len(show.days_of_week) > 0:
                    show_airs_on_day = day in show.days_of_week

            if show_airs_on_day:
                available_shows.append(show)
            else:
                unavailable_shows.append(show)

        selected_days_data.append({
            'number': day,
            'name': weekday_names[day],
            'shows': shows,  # All shows for dropdown
            'available_shows': available_shows,
            'unavailable_shows': unavailable_shows,
            'has_available_shows': len(available_shows) > 0
        })

    # Get organization settings for overlapping configuration
    try:
        org_settings = organization.settings
        allow_overlapping = org_settings.allow_overlapping_mentions
    except:
        allow_overlapping = False

    context = {
        'wizard_data': wizard_data,
        'selected_days_data': selected_days_data,
        'shows': shows,
        'allow_overlapping_mentions': allow_overlapping,
    }
    return render(request, 'mentions/wizard_step2.html', context)


@login_required
@require_permission('manage_mentions')
def recurring_wizard_step3(request):
    """Step 3: Set period and preview - Separate Page"""
    from apps.core.models import Client
    from apps.shows.models import Show
    from datetime import datetime, timedelta
    from dateutil.rrule import rrule, WEEKLY

    organization = get_current_organization(request)
    if not organization:
        messages.error(request, 'Organization context required.')
        return redirect('mentions:recurring_mentions')

    # Get wizard data and validate previous steps completion
    wizard_data = _get_wizard_data(request)
    if not wizard_data.get('schedule'):
        messages.error(request, 'Please complete the previous steps first.')
        return redirect('mentions:recurring_wizard_step1')

    if request.method == 'POST':
        start_date = request.POST.get('start_date')
        weeks = request.POST.get('weeks')
        end_date = request.POST.get('end_date')
        preview_only = request.POST.get('preview_only') == 'true'

        # Validation
        validation_errors = []

        if not start_date:
            validation_errors.append('Start date is required.')
        else:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                if start_date_obj < datetime.now().date():
                    validation_errors.append('Start date cannot be in the past.')
            except ValueError:
                validation_errors.append('Invalid start date format.')

        # Handle end date validation
        if not end_date:
            validation_errors.append('End date is required.')
        else:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                if end_date_obj <= start_date_obj:
                    validation_errors.append('End date must be after start date.')
                else:
                    # Calculate duration in days for reference
                    duration_days = (end_date_obj - start_date_obj).days + 1  # +1 to include both start and end dates
                    weeks = duration_days // 7  # For backward compatibility with existing code
            except ValueError:
                validation_errors.append('Invalid end date format.')

        # Handle AJAX preview requests
        if preview_only and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            from django.http import JsonResponse
            from django.template.loader import render_to_string

            # Re-fetch wizard data to ensure we have the latest session data
            wizard_data = _get_wizard_data(request)

            if validation_errors:
                return JsonResponse({
                    'success': False,
                    'errors': validation_errors
                })

            # Check if wizard_data has required fields
            if not wizard_data.get('schedule'):
                # Try to redirect user back to Step 2 if no schedule data
                return JsonResponse({
                    'success': False,
                    'errors': ['No schedule data found. Please complete Step 2 first.'],
                    'redirect': '/mentions/recurring/wizard/schedule/'
                })

            # 🔧 FIX: Save the current form values to session during AJAX preview
            # This ensures that when user clicks quick buttons like "2 Months",
            # the values are saved immediately
            wizard_data.update({
                'start_date': start_date,
                'end_date': end_date,
                'weeks': int(weeks) if weeks else None  # Keep for backward compatibility
            })
            request.session['simple_recurring_wizard'] = wizard_data
            request.session.modified = True

            logger.info(f"🔧 AJAX Preview: Updated wizard data with start_date={start_date}, end_date={end_date}")

            # Generate preview data with new parameters
            try:
                preview_data, statistics = _generate_preview_data(
                    organization, wizard_data, start_date, end_date
                )
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'errors': [f'Error generating preview: {str(e)}']
                })

            # Render preview HTML
            try:
                preview_html = render_to_string('mentions/wizard_preview_partial.html', {
                    'preview_data': preview_data,
                    'statistics': statistics,
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'errors': [f'Error rendering preview: {str(e)}']
                })

            return JsonResponse({
                'success': True,
                'preview_html': preview_html,
                'statistics': statistics
            })

        if validation_errors:
            for error in validation_errors:
                messages.error(request, error)
        else:
            wizard_data.update({
                'start_date': start_date,
                'end_date': end_date,
                'weeks': int(weeks) if weeks else None  # Keep for backward compatibility
            })
            request.session['simple_recurring_wizard'] = wizard_data
            return redirect('mentions:recurring_wizard_save')

    # Generate enhanced preview
    client = Client.objects.get(id=wizard_data['client_id'])

    # Use provided start date or default to today
    start_date_str = wizard_data.get('start_date')
    if not start_date_str:
        # Set default start date to today and save it in wizard_data
        start_date_str = datetime.now().date().strftime('%Y-%m-%d')
        wizard_data['start_date'] = start_date_str
        request.session['simple_recurring_wizard'] = wizard_data

    weeks_count = wizard_data.get('weeks', 1)

    # Debug the wizard data before preview generation
    logger.info(f"Step 3 preview generation debug:")
    logger.info(f"  Wizard schedule data: {wizard_data.get('schedule', 'NOT_SET')}")
    logger.info(f"  Selected days: {wizard_data.get('selected_days', 'NOT_SET')}")
    logger.info(f"  Total slots from wizard: {wizard_data.get('total_slots', 'NOT_SET')}")

    preview_data, statistics = _generate_preview_data(
        organization, wizard_data, start_date_str, weeks_count
    )

    logger.info(f"  Preview statistics: {statistics}")

    # Generate submission token if not exists
    if not wizard_data.get('submission_token'):
        import uuid
        wizard_data['submission_token'] = str(uuid.uuid4())
        request.session['simple_recurring_wizard'] = wizard_data
        request.session.modified = True

    context = {
        'wizard_data': wizard_data,
        'client': client,
        'preview_data': preview_data,
        'total_mentions_preview': statistics['total_mentions_preview'],
        'estimated_total_mentions': statistics['estimated_total_mentions'],
        'mentions_per_week': statistics['mentions_per_week'],
        'total_days': statistics['total_days'],
        'duration_text': statistics['duration_text'],
        'cost_estimate': statistics['cost_estimate'],
        'preview_days': statistics['preview_days'],
        'today': datetime.now().date(),
    }
    return render(request, 'mentions/wizard_step3.html', context)


@login_required
@require_permission('manage_mentions')
def recurring_wizard_step_legacy(request, step):
    """Legacy URL support - redirect to new separate page structure"""
    if step == 1:
        return redirect('mentions:recurring_wizard_step1')
    elif step == 2:
        return redirect('mentions:recurring_wizard_step2')
    elif step == 3:
        return redirect('mentions:recurring_wizard_step3')
    else:
        messages.error(request, 'Invalid step.')
        return redirect('mentions:recurring_wizard_step1')


@login_required
def wizard_session_debug(request):
    """Debug endpoint to check session data (development only)"""
    from django.http import JsonResponse
    from django.conf import settings

    if not settings.DEBUG:
        return JsonResponse({'error': 'Debug mode only'}, status=403)

    session_data = dict(request.session)
    return JsonResponse({
        'session_key': request.session.session_key,
        'session_data': session_data,
        'wizard_data_exists': 'simple_recurring_wizard' in request.session,
        'session_age': request.session.get_expiry_age(),
        'session_expires_at_browser_close': request.session.get_expire_at_browser_close(),
    })


def _get_wizard_data(request):
    """Get wizard data from session with version checking"""
    wizard_data = request.session.get('simple_recurring_wizard', {})

    # Add version checking to detect stale data
    current_version = "2.1"  # Increment this when making breaking changes
    if wizard_data.get('version') != current_version:
        # Only clear if we have no important data, otherwise just update version
        if not wizard_data.get('schedule') and not wizard_data.get('selected_days'):
            # Clear stale data and start fresh
            wizard_data = {'version': current_version}
        else:
            # Preserve existing data but update version
            wizard_data['version'] = current_version
        request.session['simple_recurring_wizard'] = wizard_data

    return wizard_data


# @login_required
# @require_permission('manage_mentions')
# def recurring_wizard_save(request):
#     """Save the recurring mention"""
#     from .models import RecurringMention, RecurringMentionShow
#     from apps.core.models import Client
#     from apps.shows.models import Show
#     from datetime import datetime, timedelta

#     # Only allow POST requests (forms should submit via POST)
#     if request.method != 'POST':
#         messages.error(request, 'Invalid request method. Please use the form to create recurring mentions.')
#         return redirect('mentions:recurring_wizard_step3')

#     # Debug logging for CSRF and request info
#     import logging
#     logger = logging.getLogger(__name__)
#     logger.info(f"POST request received from user {request.user.id}")
#     logger.info(f"CSRF token in POST: {request.POST.get('csrfmiddlewaretoken', 'NOT_FOUND')}")
#     logger.info(f"Session key: {request.session.session_key}")
#     logger.info(f"Request headers: {dict(request.headers)}")

#     organization = get_current_organization(request)
#     wizard_data = request.session.get('simple_recurring_wizard', {})

#     if not all([wizard_data.get('title'), wizard_data.get('schedule'), wizard_data.get('start_date')]):
#         messages.error(request, 'Invalid wizard data.')
#         return redirect('mentions:recurring_wizard_step1')

#     # Check if wizard data has already been used (prevent session reuse)
#     if wizard_data.get('used', False):
#         messages.error(request, 'This wizard session has already been used. Please start a new recurring mention.')
#         if 'simple_recurring_wizard' in request.session:
#             del request.session['simple_recurring_wizard']
#         return redirect('mentions:recurring_wizard_step1')

#     # Generate a unique submission token to prevent duplicate processing
#     import uuid
#     submission_token = str(uuid.uuid4())

#     # Check if this exact submission has already been processed
#     processed_tokens = request.session.get('processed_submission_tokens', [])
#     form_token = request.POST.get('submission_token', '')

#     if form_token and form_token in processed_tokens:
#         messages.warning(request, 'This form has already been submitted. Redirecting to avoid duplicates.')
#         return redirect('mentions:recurring_mentions')

#     # Mark wizard data as used to prevent reuse
#     wizard_data['used'] = True
#     wizard_data['submission_token'] = submission_token
#     request.session['simple_recurring_wizard'] = wizard_data
#     request.session.modified = True

#     # Add this token to processed tokens
#     if form_token:
#         processed_tokens.append(form_token)
#         request.session['processed_submission_tokens'] = processed_tokens[-10:]  # Keep only last 10 tokens
#         request.session.modified = True

#     # Check for potential duplicate submissions by looking for recent similar recurring mentions
#     # This helps prevent double-click issues
#     client_id = wizard_data.get('client_id')
#     title = wizard_data.get('title')
#     start_date_str = wizard_data.get('start_date')

#     if client_id and title and start_date_str:
#         try:
#             start_date_obj = datetime.strptime(start_date_str, '%Y-%m-%d').date()
#             # Check for duplicate recurring mentions created in the last 5 minutes with same details
#             recent_duplicates = RecurringMention.objects.filter(
#                 client_id=client_id,
#                 title=title,
#                 start_date=start_date_obj,
#                 created_by=request.user,
#                 created_at__gte=datetime.now() - timedelta(minutes=5)
#             )

#             if recent_duplicates.exists():
#                 messages.warning(request, 'A similar recurring mention was just created. Redirecting to avoid duplicates.')
#                 # Clear wizard data to prevent further attempts
#                 if 'simple_recurring_wizard' in request.session:
#                     del request.session['simple_recurring_wizard']
#                 return redirect('mentions:recurring_mentions')
#         except ValueError:
#             pass  # Invalid date format, continue with creation

#     # Use database transaction with select_for_update to prevent race conditions
#     from django.db import transaction

#     # try:
#     #     with transaction.atomic():
#     #         # Add debug logging
#     #         import logging
#     #         logger = logging.getLogger(__name__)
#     #         logger.info(f"Creating recurring mention for user {request.user.id} with title '{wizard_data.get('title')}'")

#     #         # Create recurring mention
#     #         client = Client.objects.get(id=wizard_data['client_id'])
#     #         start_date = datetime.strptime(wizard_data['start_date'], '%Y-%m-%d').date()
#     #         end_date = start_date + timedelta(weeks=wizard_data.get('weeks',4))

#     #         # Check for existing recurring mention with exact same parameters
#     #         # Use select_for_update to lock the query and prevent race conditions
#     #         existing_recurring = RecurringMention.objects.select_for_update().filter(
#     #             title=wizard_data['title'],
#     #             client=client,
#     #             start_date=start_date,
#     #             created_by=request.user,
#     #             created_at__gte=datetime.now() - timedelta(minutes=10)  # Within last 10 minutes
#     #         ).first()

#     #         if existing_recurring:
#     #             logger.info(f"Recurring mention already exists with ID {existing_recurring.id}, skipping creation")
#     #             messages.warning(request, 'This recurring mention was just created. Redirecting to avoid duplicates.')
#     #             # Clear wizard data
#     #             if 'simple_recurring_wizard' in request.session:
#     #                 del request.session['simple_recurring_wizard']
#     #             return redirect('mentions:recurring_mentions')

#     #         # Create new recurring mention
#     #         recurring_mention = RecurringMention.objects.create(
#     #             title=wizard_data['title'],
#     #             content=wizard_data['content'],
#     #             client=client,
#     #             priority=wizard_data.get('priority', 2),
#     #             duration_seconds=wizard_data.get('duration_seconds', 30),
#     #             frequency='weekly',
#     #             weekdays=wizard_data['selected_days'],
#     #             start_date=start_date,
#     #             end_date=end_date,
#     #             is_active=True,
#     #             created_by=request.user
#     #         )

#     #         logger.info(f"Successfully created recurring mention with ID {recurring_mention.id}")

#     #     # FIXED: Create show assignments only for unique show/time combinations
#     #     # Avoid creating duplicate RecurringMentionShow records that cause multiplication
#     #     unique_show_times = set()  # Track (show_id, time) combinations to avoid duplicates

#     #     # Collect all unique show/time combinations from all selected days
#     #     for day, day_schedule in wizard_data['schedule'].items():
#     #         for item in day_schedule:
#     #             show_id = item['show_id']
#     #             # Convert time string to time object
#     #             if isinstance(item['time'], str):
#     #                 scheduled_time = parse_time_string(item['time'])
#     #             else:
#     #                 scheduled_time = item['time']

#     #             # Add to unique combinations
#     #             unique_show_times.add((show_id, scheduled_time))

#     #     # Create RecurringMentionShow records for unique combinations only
#     #     for show_id, scheduled_time in unique_show_times:
#     #         show = Show.objects.get(id=show_id)
#     #         RecurringMentionShow.objects.create(
#     #             recurring_mention=recurring_mention,
#     #             show=show,
#     #             scheduled_time=scheduled_time
#     #         )

#     #     # Generate initial mentions
#     #     generated_mentions = recurring_mention.generate_mentions()

#     #     # Clear wizard data to prevent reuse
#     #     if 'simple_recurring_wizard' in request.session:
#     #         del request.session['simple_recurring_wizard']

#     #     # Force session save to ensure wizard data is cleared
#     #     request.session.modified = True
#     #     request.session.save()

#     #     messages.success(request, f'Recurring mention created successfully! Generated {len(generated_mentions)} mentions.')
#     #     return redirect('mentions:recurring_mentions')

#     # except Exception as e:
#     #     import logging
#     #     logger = logging.getLogger(__name__)
#     #     logger.error(f'Error creating recurring mention: {str(e)}', exc_info=True)

#     #     # Provide more specific error messages
#     #     error_msg = str(e)
#     #     if 'unique constraint' in error_msg.lower() and 'recurringmentionshow' in error_msg:
#     #         messages.error(request, 'Error: Duplicate show/time combination detected. Please ensure each show and time combination is unique in your schedule.')
#     #     else:
#     #         messages.error(request, f'Error creating recurring mention: {error_msg}')
#     #     return redirect('mentions:recurring_wizard_step', step=3)

@login_required
@require_permission('manage_mentions')
@require_http_methods(["POST"])
def recurring_wizard_save(request):
    """
    Save the recurring mention with comprehensive duplicate prevention.
    
    This view handles the final step of the recurring mention wizard,
    creating the recurring mention and associated show assignments.
    """
    
    # Log request details for debugging
    _log_request_details(request)
    
    # Get organization context
    organization = get_current_organization(request)
    
    # Validate and retrieve wizard data
    wizard_data = _get_and_validate_wizard_data(request)
    if not wizard_data:
        return redirect('mentions:recurring_wizard_step1')
    
    # Check for session reuse
    if _is_wizard_session_used(wizard_data):
        _clear_wizard_session(request)
        messages.error(request, 'This wizard session has already been used. Please start a new recurring mention.')
        return redirect('mentions:recurring_wizard_step1')
    
    # Handle submission token duplicate prevention
    duplicate_check_result = _check_submission_duplicates(request, wizard_data)
    if duplicate_check_result:
        return duplicate_check_result
    
    # Check for recent similar recurring mentions
    recent_duplicate_result = _check_recent_duplicates(request, wizard_data)
    if recent_duplicate_result:
        return recent_duplicate_result
    
    # Mark wizard as used and save submission token
    _mark_wizard_used(request, wizard_data)
    
    # Create the recurring mention
    try:
        return _create_recurring_mention(request, wizard_data)
    except Exception as e:
        logger.error(f'Unexpected error in recurring_wizard_save: {str(e)}', exc_info=True)
        messages.error(request, 'An unexpected error occurred. Please try again.')
        return redirect('mentions:recurring_wizard_step3')


def _log_request_details(request):
    """Log request details for debugging purposes."""
    logger.info(f"POST request received from user {request.user.id}")
    logger.info(f"CSRF token in POST: {request.POST.get('csrfmiddlewaretoken', 'NOT_FOUND')}")
    logger.info(f"Session key: {request.session.session_key}")
    logger.info(f"Request headers: {dict(request.headers)}")


def _get_and_validate_wizard_data(request):
    """
    Retrieve and validate wizard data from session.
    
    Returns:
        dict or None: Wizard data if valid, None otherwise
    """
    wizard_data = request.session.get('simple_recurring_wizard', {})
    
    required_fields = ['title', 'schedule', 'start_date', 'client_id', 'content']
    missing_fields = [field for field in required_fields if not wizard_data.get(field)]
    
    if missing_fields:
        logger.warning(f"Missing required wizard fields: {missing_fields}")
        messages.error(request, f'Invalid wizard data. Missing: {", ".join(missing_fields)}')
        return None
    
    return wizard_data


def _is_wizard_session_used(wizard_data):
    """Check if wizard session has already been used."""
    return wizard_data.get('used', False)


def _check_submission_duplicates(request, wizard_data):
    """
    Check for duplicate form submissions using submission tokens.
    
    Returns:
        HttpResponse or None: Redirect response if duplicate found, None otherwise
    """
    processed_tokens = request.session.get('processed_submission_tokens', [])
    form_token = request.POST.get('submission_token', '')
    
    if form_token and form_token in processed_tokens:
        logger.warning(f"Duplicate submission token detected: {form_token}")
        messages.warning(request, 'This form has already been submitted. Redirecting to avoid duplicates.')
        return redirect('mentions:recurring_mentions')
    
    return None


def _check_recent_duplicates(request, wizard_data):
    """
    Check for recently created similar recurring mentions.
    
    Returns:
        HttpResponse or None: Redirect response if duplicate found, None otherwise
    """
    client_id = wizard_data.get('client_id')
    title = wizard_data.get('title')
    start_date_str = wizard_data.get('start_date')
    
    if not all([client_id, title, start_date_str]):
        return None
    
    try:
        start_date_obj = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        cutoff_time = timezone.now() - timedelta(minutes=5)
        
        recent_duplicates = RecurringMention.objects.filter(
            client_id=client_id,
            title=title,
            start_date=start_date_obj,
            created_by=request.user,
            created_at__gte=cutoff_time
        )
        
        if recent_duplicates.exists():
            logger.warning(f"Recent duplicate recurring mention found for user {request.user.id}")
            _clear_wizard_session(request)
            messages.warning(request, 'A similar recurring mention was just created. Redirecting to avoid duplicates.')
            return redirect('mentions:recurring_mentions')
            
    except ValueError as e:
        logger.warning(f"Invalid date format in wizard data: {start_date_str}")
    
    return None


def _mark_wizard_used(request, wizard_data):
    """Mark wizard as used and save submission token."""
    submission_token = str(uuid.uuid4())
    form_token = request.POST.get('submission_token', '')
    
    # Mark wizard data as used
    wizard_data['used'] = True
    wizard_data['submission_token'] = submission_token
    request.session['simple_recurring_wizard'] = wizard_data
    
    # Add form token to processed tokens
    if form_token:
        processed_tokens = request.session.get('processed_submission_tokens', [])
        processed_tokens.append(form_token)
        # Keep only last 10 tokens to prevent session bloat
        request.session['processed_submission_tokens'] = processed_tokens[-10:]
    
    request.session.modified = True


def _create_recurring_mention(request, wizard_data):
    """
    Create the recurring mention and associated show assignments.
    
    Returns:
        HttpResponse: Redirect response
    """
    try:
        # Debug logging
        logger.info(f"Starting creation with data: {wizard_data}")
        
        with transaction.atomic():
            logger.info(f"Creating recurring mention for user {request.user.id} with title '{wizard_data.get('title')}'")
            
            # Get client and calculate dates
            try:
                client = Client.objects.get(id=wizard_data['client_id'])
                logger.info(f"Found client: {client}")
            except Client.DoesNotExist:
                logger.error(f"Client not found with ID: {wizard_data['client_id']}")
                raise
            
            # try:
            #     start_date = datetime.strptime(wizard_data['start_date'], '%Y-%m-%d').date()
            #     end_date = start_date + timedelta(weeks=wizard_data.get('weeks', 1))
            #     logger.info(f"Dates calculated - Start: {start_date}, End: {end_date}")
            # except ValueError as e:
            #     logger.error(f"Date parsing error: {e}")
            #     raise ValueError(f"Invalid date format: {wizard_data['start_date']}")
            try:
                start_str = wizard_data['start_date']
                end_str = wizard_data['end_date']
                start_date = datetime.strptime(start_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_str, '%Y-%m-%d').date()

                # Calculate duration for reference
                duration_days = (end_date - start_date).days + 1  # +1 to include both dates

                logger.info(f"🔍 WIZARD DATA DEBUG:")
                logger.info(f"  Raw wizard_data: {wizard_data}")
                logger.info(f"  Start date string: '{start_str}'")
                logger.info(f"  End date string: '{end_str}'")
                logger.info(f"  Parsed start_date: {start_date}")
                logger.info(f"  Parsed end_date: {end_date}")
                logger.info(f"  Campaign duration: {duration_days} days")
            except KeyError as e:
                logger.error(f"Missing required field in wizard_data: {e}")
                raise ValueError(f"Missing required field: {e}")
            except ValueError as e:
                logger.error(f"Date parsing error: {e}")
                raise ValueError(f"Invalid date format: {e}")

            
            # Final duplicate check with database lock
            existing_recurring = RecurringMention.objects.select_for_update().filter(
                title=wizard_data['title'],
                client=client,
                start_date=start_date,
                created_by=request.user,
                created_at__gte=timezone.now() - timedelta(minutes=10)
            ).first()
            
            if existing_recurring:
                logger.info(f"Recurring mention already exists with ID {existing_recurring.id}")
                _clear_wizard_session(request)
                messages.warning(request, 'This recurring mention was just created. Redirecting to avoid duplicates.')
                return redirect('mentions:recurring_mentions')
            
            # Validate required fields before creation
            required_fields = {
                'title': wizard_data.get('title'),
                'content': wizard_data.get('content'),
                'selected_days': wizard_data.get('selected_days')
            }
            
            missing = [k for k, v in required_fields.items() if not v]
            if missing:
                logger.error(f"Missing required fields: {missing}")
                raise ValueError(f"Missing required fields: {', '.join(missing)}")
            
            # Create new recurring mention
            recurring_mention = RecurringMention.objects.create(
                title=wizard_data['title'],
                content=wizard_data['content'],
                client=client,
                priority=wizard_data.get('priority', 2),
                duration_seconds=max(wizard_data.get('duration_seconds', 30), 1),
                frequency='weekly',
                weekdays=wizard_data['selected_days'],
                start_date=start_date,
                end_date=end_date,
                is_active=True,
                created_by=request.user
            )
            
            logger.info(f"Successfully created recurring mention with ID {recurring_mention.id}")
        
        # Create show assignments outside the main transaction to avoid lock conflicts
        _create_show_assignments(recurring_mention, wizard_data['schedule'])
        
        # Generate initial mentions using preview logic for consistency
        try:
            organization = get_current_organization(request)

            generated_mentions = _generate_mentions_from_preview(
                organization,
                wizard_data,
                wizard_data['start_date'],
                wizard_data['end_date'],
                recurring_mention
            )
            mention_count = len(generated_mentions)
            logger.info(f"Generated {mention_count} mentions using preview logic for {duration_days} days")
        except Exception as e:
            logger.error(f"Error generating mentions: {e}")
            import traceback
            traceback.print_exc()
            # Don't fail the whole process if mention generation fails
            mention_count = 0

        # Debug: Check wizard data for slot count
        total_slots_from_wizard = wizard_data.get('total_slots', 'NOT_SET')
        logger.info(f"Final creation debug:")
        logger.info(f"  Wizard total_slots: {total_slots_from_wizard}")
        logger.info(f"  Generated mentions: {mention_count}")
        logger.info(f"  Schedule data: {wizard_data.get('schedule', 'NOT_SET')}")

        # Clean up session
        _clear_wizard_session(request)

        logger.info(f"Created recurring mention {recurring_mention.id} with {mention_count} generated mentions")
        messages.success(request, f'Recurring mention created successfully! Generated {mention_count} mentions.')
        return redirect('mentions:recurring_mentions')
        
    except Client.DoesNotExist:
        logger.error(f'Client not found: {wizard_data.get("client_id")}')
        messages.error(request, 'Selected client not found. Please try again.')
        return redirect('mentions:recurring_wizard_step1')
        
    except ValueError as e:
        logger.error(f'Validation error: {str(e)}')
        messages.error(request, f'Validation error: {str(e)}')
        return redirect('mentions:recurring_wizard_step1')
        
    except Exception as e:
        logger.error(f'Error creating recurring mention: {str(e)}', exc_info=True)
        logger.error(f'Wizard data: {wizard_data}')  # Log the wizard data for debugging
        return _handle_creation_error(request, e)


def _create_show_assignments(recurring_mention, schedule_data):
    """
    Create show assignments with day-specific mapping to prevent duplication.

    Args:
        recurring_mention: RecurringMention instance
        schedule_data: Dictionary containing schedule information
    """
    show_time_day_mapping = {}

    # Collect show/time combinations and track which days they're scheduled for
    for day, day_schedule in schedule_data.items():
        for item in day_schedule:
            show_id = item['show_id']

            # Convert time string to time object if needed
            if isinstance(item['time'], str):
                try:
                    scheduled_time = _parse_time_string(item['time'])
                except ValueError as e:
                    logger.error(f"Invalid time format '{item['time']}' for day {day}: {str(e)}")
                    raise ValueError(f"Invalid time format '{item['time']}' for day {day}")
            else:
                scheduled_time = item['time']

            # Create a key for this show/time combination
            key = (show_id, scheduled_time)

            # Track which days this combination is scheduled for
            if key not in show_time_day_mapping:
                show_time_day_mapping[key] = set()
            show_time_day_mapping[key].add(int(day))

    # Create RecurringMentionShow records with day information
    created_count = 0
    for (show_id, scheduled_time), scheduled_days in show_time_day_mapping.items():
        try:
            show = Show.objects.get(id=show_id)

            # Create the RecurringMentionShow record with scheduled days
            recurring_show = RecurringMentionShow.objects.create(
                recurring_mention=recurring_mention,
                show=show,
                scheduled_time=scheduled_time,
                scheduled_days=list(scheduled_days)  # Convert set to list for JSON field
            )

            created_count += 1
            logger.debug(f"Created show assignment: {show.name} at {scheduled_time} for days {scheduled_days}")

        except Show.DoesNotExist:
            logger.error(f"Show with ID {show_id} not found")
            raise ValueError(f"Show with ID {show_id} not found")

    logger.info(f"Created {created_count} show assignments for recurring mention {recurring_mention.id}")


def _parse_time_string(time_str):
    """
    Parse time string to time object.
    
    Args:
        time_str: Time string in format 'HH:MM' or 'HH:MM:SS'
        
    Returns:
        datetime.time: Parsed time object
    """
    if not isinstance(time_str, str):
        raise ValueError(f"Expected string, got {type(time_str)}")
    
    time_formats = ['%H:%M:%S', '%H:%M']
    
    for fmt in time_formats:
        try:
            return datetime.strptime(time_str, fmt).time()
        except ValueError:
            continue
    
    raise ValueError(f"Invalid time format: {time_str}. Expected HH:MM or HH:MM:SS")


def _handle_creation_error(request, error):
    """
    Handle specific creation errors with appropriate user messages.
    
    Args:
        request: Django request object
        error: Exception that occurred during creation
        
    Returns:
        HttpResponse: Redirect response with error message
    """
    error_msg = str(error).lower()
    
    # Log the full error for debugging
    logger.error(f"Creation error details: {str(error)}")
    logger.error(f"Error type: {type(error).__name__}")
    
    if 'unique constraint' in error_msg and 'recurringmentionshow' in error_msg:
        messages.error(request, 'Error: Duplicate show/time combination detected. Please ensure each show and time combination is unique in your schedule.')
        return redirect('mentions:recurring_wizard_step3')
    elif 'foreign key constraint' in error_msg:
        messages.error(request, 'Error: Invalid reference to show or client. Please check your selections.')
        return redirect('mentions:recurring_wizard_step2')
    elif 'does not exist' in error_msg:
        messages.error(request, 'Error: Referenced client or show not found. Please check your selections.')
        return redirect('mentions:recurring_wizard_step2')
    elif 'null value' in error_msg or 'not null constraint' in error_msg:
        messages.error(request, 'Error: Missing required data. Please complete all form fields.')
        return redirect('mentions:recurring_wizard_step1')
    else:
        # For debugging: show the actual error in development
        if hasattr(request, 'user') and request.user.is_superuser:
            messages.error(request, f'Debug Error: {str(error)}')
        else:
            messages.error(request, 'An unexpected error occurred while creating the recurring mention. Please try again.')
        return redirect('mentions:recurring_wizard_step3')


def _clear_wizard_session(request):
    """Clear wizard session data safely."""
    session_keys_to_clear = ['simple_recurring_wizard', 'recurring_wizard_data']
    
    for key in session_keys_to_clear:
        if key in request.session:
            del request.session[key]
    
    request.session.modified = True
    request.session.save()

# ============================================================================
# OLD WIZARD VIEWS - REMOVED FOR SIMPLICITY
# ============================================================================
# The old complex 5-step wizard has been replaced with a simple 3-step wizard
# Old files can be found in git history if needed


def toggle_recurring_mention(request, pk):
    """Toggle recurring mention active status"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    organization = get_current_organization(request)

    try:
        if organization:
            recurring_mention = get_object_or_404(
                RecurringMention,
                pk=pk,
                client__organization=organization
            )
        else:
            recurring_mention = get_object_or_404(RecurringMention, pk=pk)

        data = json.loads(request.body)
        recurring_mention.is_active = data.get('active', not recurring_mention.is_active)
        recurring_mention.save()

        return JsonResponse({
            'success': True,
            'active': recurring_mention.is_active,
            'message': f'Recurring mention {"activated" if recurring_mention.is_active else "paused"} successfully'
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)


@login_required
@require_permission('manage_mentions')
def generate_recurring_mentions(request, pk):
    """Generate mentions from recurring pattern"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    organization = get_current_organization(request)

    try:
        if organization:
            recurring_mention = get_object_or_404(
                RecurringMention,
                pk=pk,
                client__organization=organization
            )
        else:
            recurring_mention = get_object_or_404(RecurringMention, pk=pk)

        from .services import RecurringMentionService
        generated_mentions = RecurringMentionService.generate_upcoming_mentions(recurring_mention)

        return JsonResponse({
            'success': True,
            'count': len(generated_mentions),
            'message': f'Generated {len(generated_mentions)} mentions successfully'
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)


@login_required
def get_available_time_slots(request):
    """API endpoint to get available time slots for a show on a specific day"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        show_id = data.get('show_id')
        weekday = data.get('weekday')  # 0=Monday, 6=Sunday
        exclude_recurring_id = data.get('exclude_recurring_id')

        if not all([show_id, weekday is not None]):
            return JsonResponse({'error': 'Show ID and weekday are required'}, status=400)

        organization = get_current_organization(request)
        if not organization:
            return JsonResponse({'error': 'No organization found'}, status=400)

        show = get_object_or_404(Show, id=show_id, organization=organization)

        # Use pre-calculated time intervals for better performance
        time_slots = _get_optimized_time_slots_for_show(show, weekday, organization, exclude_recurring_id)

        return JsonResponse({
            'success': True,
            'show_id': show_id,
            'show_name': show.name,
            'weekday': weekday,
            'time_slots': time_slots,
            'optimized': True  # Flag to indicate we're using optimized intervals
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def _generate_time_slots_for_show(show, weekday, organization, exclude_recurring_id=None):
    """Generate available time slots for a show on a specific weekday"""
    from datetime import datetime, timedelta
    from .models import MentionReading, RecurringMention

    time_slots = []

    # Generate 30-second intervals within the show's time range
    # Limit to reasonable number of slots to avoid overwhelming dropdown
    start_time = show.start_time
    end_time = show.end_time

    # Handle shows that cross midnight
    start_datetime = datetime.combine(datetime.today(), start_time)
    end_datetime = datetime.combine(datetime.today(), end_time)

    if end_time <= start_time:
        # Show crosses midnight
        end_datetime += timedelta(days=1)

    # Calculate show duration and limit slots if too long
    show_duration_minutes = (end_datetime - start_datetime).total_seconds() / 60

    # Use 30-second intervals for shows up to 2 hours, otherwise use 1-minute intervals
    if show_duration_minutes <= 120:  # 2 hours
        interval_seconds = 30
        max_slots = int(show_duration_minutes * 2)  # 2 slots per minute
    else:
        interval_seconds = 60  # 1-minute intervals for longer shows
        max_slots = int(show_duration_minutes)

    # Cap at 240 slots maximum to keep dropdown manageable
    if max_slots > 240:
        interval_seconds = int((show_duration_minutes * 60) / 240)  # Adjust interval
        max_slots = 240

    current_time = start_datetime
    slot_count = 0

    while current_time < end_datetime and slot_count < max_slots:
        time_str = current_time.strftime('%H:%M:%S')
        time_obj = current_time.time()

        # Check if this time slot is available
        is_available = _is_time_slot_available(
            show, time_obj, weekday, organization, exclude_recurring_id
        )

        # Get conflict information (including same-day conflicts)
        conflict_info = _get_time_slot_conflicts(
            show, time_obj, weekday, organization, exclude_recurring_id
        )

        time_slots.append({
            'time': time_str,
            'time_display': current_time.strftime('%I:%M:%S %p'),
            'is_available': is_available,
            'conflicts': conflict_info['conflicts'],
            'warnings': conflict_info['warnings']
        })

        current_time += timedelta(seconds=interval_seconds)
        slot_count += 1

    return time_slots


def _get_optimized_time_slots_for_show(show, weekday, organization, exclude_recurring_id=None):
    """Get pre-calculated time slots for a show with optimized performance"""
    try:
        # First, try to get cached time intervals from the show
        time_slots = show.get_cached_time_intervals(weekday=weekday, exclude_recurring_id=exclude_recurring_id)

        if time_slots:
            return time_slots

        # If no pre-calculated intervals exist, generate them on-the-fly and store them
        if not show.time_intervals.exists():
            logger.info(f"No time intervals found for show {show.name}, generating them now...")
            intervals_created = show.generate_time_intervals(interval_seconds=30)
            logger.info(f"Generated {intervals_created} time intervals for show {show.name}")

        # Try again to get the cached intervals
        time_slots = show.get_cached_time_intervals(weekday=weekday, exclude_recurring_id=exclude_recurring_id)

        if time_slots:
            return time_slots

        # Fallback to the original method if something went wrong
        logger.warning(f"Falling back to original time slot generation for show {show.name}")
        return _generate_time_slots_for_show(show, weekday, organization, exclude_recurring_id)

    except Exception as e:
        logger.error(f"Error in optimized time slot generation for show {show.name}: {str(e)}")
        # Fallback to the original method
        return _generate_time_slots_for_show(show, weekday, organization, exclude_recurring_id)


def _is_time_slot_available(show, time_obj, weekday, organization, exclude_recurring_id=None):
    """Check if a specific time slot is available"""
    from .models import RecurringMentionShow, MentionReading
    from datetime import datetime, timedelta

    # Check for existing recurring mentions on this show/day/time
    recurring_show_assignments = RecurringMentionShow.objects.filter(
        show=show,
        scheduled_time=time_obj,
        recurring_mention__client__organization=organization,
        recurring_mention__is_active=True,
        is_active=True
    ).select_related('recurring_mention')

    if exclude_recurring_id:
        recurring_show_assignments = recurring_show_assignments.exclude(
            recurring_mention_id=exclude_recurring_id
        )

    for assignment in recurring_show_assignments:
        recurring = assignment.recurring_mention
        # Check if this recurring mention includes the specified weekday
        if hasattr(recurring, 'weekdays') and recurring.weekdays:
            if weekday in recurring.weekdays:
                return False

    # Check for existing mentions on specific dates (for the next 30 days)
    # We check multiple dates to cover the recurring mention period
    today = datetime.now().date()
    for days_ahead in range(0, 30):  # Check next 30 days
        check_date = today + timedelta(days=days_ahead)
        if check_date.weekday() == weekday:  # Only check dates that match the weekday
            existing_readings = MentionReading.objects.filter(
                show=show,
                scheduled_date=check_date,
                scheduled_time=time_obj,
                actual_read_time__isnull=True,  # Only unread mentions
                mention__client__organization=organization
            )

            if existing_readings.exists():
                return False

    return True


def _get_time_slot_conflicts(show, time_obj, weekday, organization, exclude_recurring_id=None):
    """Get detailed conflict information for a time slot with caching"""
    from .models import RecurringMentionShow
    from datetime import datetime
    from django.core.cache import cache
    import hashlib

    # Create cache key based on parameters
    cache_key_data = f"conflicts_{show.id}_{time_obj}_{weekday}_{organization.id}_{exclude_recurring_id or 'none'}"
    cache_key = hashlib.md5(cache_key_data.encode()).hexdigest()

    # Try to get from cache first (cache for 5 minutes)
    cached_result = cache.get(cache_key)
    if cached_result is not None:
        return cached_result

    conflicts = []
    warnings = []

    # Check if overlapping mentions are allowed in organization settings
    try:
        settings = organization.settings
        if settings.allow_overlapping_mentions:
            # Skip conflict checking if overlapping mentions are allowed
            result = {'conflicts': conflicts, 'warnings': warnings}
            cache.set(cache_key, result, 300)  # Cache for 5 minutes
            return result
    except:
        # If we can't get settings, proceed with conflict detection as default
        pass

    # Check for recurring mention conflicts
    recurring_show_assignments = RecurringMentionShow.objects.filter(
        show=show,
        recurring_mention__client__organization=organization,
        recurring_mention__is_active=True,
        is_active=True
    ).select_related('recurring_mention')

    if exclude_recurring_id:
        recurring_show_assignments = recurring_show_assignments.exclude(
            recurring_mention_id=exclude_recurring_id
        )

    for assignment in recurring_show_assignments:
        recurring = assignment.recurring_mention
        scheduled_time = assignment.scheduled_time

        # Check if this recurring mention includes the specified weekday
        if hasattr(recurring, 'weekdays') and recurring.weekdays:
            if weekday in recurring.weekdays:
                # Check for exact conflicts
                if scheduled_time == time_obj:
                    conflicts.append({
                        'type': 'exact_conflict',
                        'message': f"'{recurring.title}' already scheduled",
                        'severity': 'error'
                    })
                else:
                    # Check for close timing (within 15 minutes)
                    try:
                        scheduled_dt = datetime.combine(datetime.today(), scheduled_time)
                        current_dt = datetime.combine(datetime.today(), time_obj)
                        time_diff = abs((scheduled_dt - current_dt).total_seconds() / 60)

                        if time_diff <= 1 and time_diff > 0:  # Within 1 minute
                            warnings.append({
                                'type': 'close_timing',
                                'message': f"Close to '{recurring.title}' at {scheduled_time.strftime('%H:%M:%S')}",
                                'severity': 'warning'
                            })
                    except (ValueError, TypeError):
                        pass

    # Check for same-day mention conflicts (for the next 30 days) - OPTIMIZED
    from .models import MentionReading
    from datetime import timedelta

    today = datetime.now().date()
    end_date = today + timedelta(days=30)

    # Generate all dates for the specified weekday in the next 30 days
    check_dates = []
    current_date = today
    while current_date <= end_date:
        if current_date.weekday() == weekday:
            check_dates.append(current_date)
        current_date += timedelta(days=1)

    if check_dates:
        # Single optimized query to get all conflicting readings for all dates at once
        existing_readings = MentionReading.objects.filter(
            show=show,
            scheduled_date__in=check_dates,
            actual_read_time__isnull=True,  # Only unread mentions
            mention__client__organization=organization
        ).select_related('mention', 'mention__client').order_by('scheduled_date', 'scheduled_time')

        for reading in existing_readings:
            # Check for exact time conflicts
            if reading.scheduled_time == time_obj:
                conflicts.append({
                    'type': 'same_day_exact_conflict',
                    'message': f"'{reading.mention.title}' already scheduled on {reading.scheduled_date.strftime('%m/%d')}",
                    'severity': 'error'
                })
            else:
                # Check for close timing (within 1 minute)
                try:
                    scheduled_dt = datetime.combine(datetime.today(), reading.scheduled_time)
                    current_dt = datetime.combine(datetime.today(), time_obj)
                    time_diff = abs((scheduled_dt - current_dt).total_seconds() / 60)

                    if time_diff <= 1 and time_diff > 0:  # Within 1 minute
                        warnings.append({
                            'type': 'same_day_close_timing',
                            'message': f"Close to '{reading.mention.title}' at {reading.scheduled_time.strftime('%H:%M:%S')} on {reading.scheduled_date.strftime('%m/%d')}",
                            'severity': 'warning'
                        })
                except (ValueError, TypeError):
                    pass

    # Cache the result for 5 minutes
    result = {
        'conflicts': conflicts,
        'warnings': warnings
    }
    cache.set(cache_key, result, 300)  # Cache for 5 minutes
    return result


@login_required
def check_time_slot_conflicts(request):
    """API endpoint to check for time slot conflicts in real-time"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        show_id = data.get('show_id')
        time_str = data.get('time')
        date_str = data.get('date')
        weekday = data.get('weekday')  # 0=Monday, 6=Sunday
        exclude_recurring_id = data.get('exclude_recurring_id')

        if not all([show_id, time_str]):
            return JsonResponse({'error': 'Show ID and time are required'}, status=400)

        organization = get_current_organization(request)
        show = get_object_or_404(Show, id=show_id, organization=organization)
        scheduled_time = parse_time_string(time_str)

        conflicts = []
        warnings = []

        # Check for existing recurring mentions conflicts
        if weekday is not None:
            recurring_conflicts = _check_recurring_conflicts(
                show, scheduled_time, weekday, organization,
                exclude_recurring_mention_id=exclude_recurring_id
            )
            for conflict in recurring_conflicts:
                conflicts.append({
                    'type': 'recurring',
                    'message': f"Recurring conflict: {conflict}",
                    'severity': 'error'
                })

        # Check for specific date conflicts if date is provided
        if date_str:
            try:
                scheduled_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                date_conflicts = _check_specific_date_conflicts(
                    show, scheduled_time, scheduled_date, organization
                )
                for conflict in date_conflicts:
                    conflicts.append({
                        'type': 'specific_date',
                        'message': conflict['message'],
                        'severity': conflict['severity']
                    })
            except ValueError:
                pass  # Invalid date format, skip date-specific checks

        # Check show capacity and time range
        capacity_warnings = _check_show_capacity_warnings(show, scheduled_time)
        warnings.extend(capacity_warnings)

        return JsonResponse({
            'success': True,
            'has_conflicts': len(conflicts) > 0,
            'conflicts': conflicts,
            'warnings': warnings,
            'show_name': show.name,
            'time_slot': time_str
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def _check_specific_date_conflicts(show, scheduled_time, scheduled_date, organization):
    """Check for conflicts on a specific date"""
    from .models import MentionReading
    from datetime import datetime, timedelta

    conflicts = []

    # Check if overlapping mentions are allowed in organization settings
    try:
        settings = organization.settings
        if settings.allow_overlapping_mentions:
            # Skip conflict checking if overlapping mentions are allowed
            return conflicts
    except:
        # If we can't get settings, proceed with conflict detection as default
        pass

    # Combine date and time for comparison
    scheduled_datetime = datetime.combine(scheduled_date, scheduled_time)

    # Find existing mentions scheduled for the same show on the same date
    existing_readings = MentionReading.objects.filter(
        show=show,
        scheduled_date=scheduled_date,
        actual_read_time__isnull=True  # Only unread mentions
    ).select_related('mention', 'mention__client')

    for reading in existing_readings:
        reading_datetime = datetime.combine(reading.scheduled_date, reading.scheduled_time)

        # Check for exact time conflicts
        if reading.scheduled_time == scheduled_time:
            conflicts.append({
                'message': f"Exact time conflict with '{reading.mention.title}' (Client: {reading.mention.client.name})",
                'severity': 'error',
                'reading_id': reading.id
            })
        else:
            # Check for overlapping time ranges based on duration
            reading_end = reading_datetime + timedelta(seconds=reading.mention.duration_seconds)
            scheduled_end = scheduled_datetime + timedelta(seconds=30)  # Default duration

            # Check if times overlap
            if (scheduled_datetime < reading_end and scheduled_end > reading_datetime):
                time_diff = abs((reading_datetime - scheduled_datetime).total_seconds())
                if time_diff < 300:  # Within 5 minutes
                    conflicts.append({
                        'message': f"Close timing with '{reading.mention.title}' at {reading.scheduled_time.strftime('%H:%M')} (Client: {reading.mention.client.name})",
                        'severity': 'warning',
                        'reading_id': reading.id
                    })

    return conflicts


def _check_show_capacity_warnings(show, scheduled_time):
    """Check for show capacity and timing warnings"""
    warnings = []

    # Check if time is within show's broadcast hours
    if hasattr(show, 'start_time') and hasattr(show, 'end_time') and show.start_time and show.end_time:
        if not (show.start_time <= scheduled_time <= show.end_time):
            warnings.append({
                'type': 'time_range',
                'message': f"Time {scheduled_time.strftime('%H:%M')} is outside show hours ({show.start_time.strftime('%H:%M')} - {show.end_time.strftime('%H:%M')})",
                'severity': 'warning'
            })

    # Check for peak hours (example: 7-9 AM, 5-7 PM)
    hour = scheduled_time.hour
    if (7 <= hour <= 9) or (17 <= hour <= 19):
        warnings.append({
            'type': 'peak_hours',
            'message': f"Peak listening hours - consider spacing mentions appropriately",
            'severity': 'info'
        })

    return warnings


@login_required
@require_permission('manage_mentions')
def quick_create_mention(request):
    """Quick create single mention"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    organization = get_current_organization(request)

    try:
        from apps.core.models import Client, Presenter
        from apps.shows.models import Show

        # Get form data
        client_id = request.POST.get('client')
        show_id = request.POST.get('show')
        title = request.POST.get('title')
        content = request.POST.get('content')
        date_str = request.POST.get('date')
        time_str = request.POST.get('time')
        duration = int(request.POST.get('duration', 30))

        # Validate required fields
        if not all([client_id, show_id, title, content, date_str, time_str]):
            return JsonResponse({'error': 'All fields are required'}, status=400)

        # Get objects
        client = get_object_or_404(Client, id=client_id, organization=organization)
        show = get_object_or_404(Show, id=show_id, organization=organization)

        # Parse date and time
        scheduled_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        scheduled_time = parse_time_string(time_str)

        # Create mention
        mention = Mention.objects.create(
            title=title,
            content=content,
            client=client,
            duration_seconds=duration,
            status='pending',
            created_by=request.user
        )

        # Get primary presenter for the show, or any available presenter
        presenter_obj = None
        primary_presenter = show.showpresenter_set.filter(is_primary=True, is_active=True).first()
        if primary_presenter:
            presenter_obj = primary_presenter.presenter
        else:
            # Fall back to any active presenter for this show
            any_presenter = show.showpresenter_set.filter(is_active=True).first()
            if any_presenter:
                presenter_obj = any_presenter.presenter
            else:
                # Last resort: any active presenter in the organization
                presenter_obj = Presenter.objects.filter(organization=organization, is_active=True).first()

        if presenter_obj:
            # Schedule the mention
            reading = MentionReading.objects.create(
                mention=mention,
                show=show,
                presenter=presenter_obj,
                scheduled_date=scheduled_date,
                scheduled_time=scheduled_time
            )
        else:
            # Create mention without reading - can be assigned later
            pass

        return JsonResponse({
            'success': True,
            'mention_id': mention.id,
            'message': 'Single mention created successfully'
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)


@login_required
def recurring_mention_edit(request, pk):
    """Edit recurring mention"""
    from .models import RecurringMention, RecurringMentionShow
    from .services import RecurringMentionService

    organization = get_current_organization(request)

    if organization:
        recurring_mention = get_object_or_404(
            RecurringMention,
            pk=pk,
            client__organization=organization
        )
    else:
        recurring_mention = get_object_or_404(RecurringMention, pk=pk)

    if request.method == 'POST':
        try:
            # Parse dates properly
            start_date_str = request.POST.get('start_date')
            end_date_str = request.POST.get('end_date')

            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else None
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date() if end_date_str else None

            # Get campaign tracking fields
            campaign_name = request.POST.get('campaign_name', '').strip()
            daily_frequency = int(request.POST.get('daily_frequency', 1))
            total_required = request.POST.get('total_required')
            total_required = int(total_required) if total_required else None

            # Update recurring mention
            update_data = {
                'title': request.POST.get('title'),
                'content': request.POST.get('content'),
                'priority': int(request.POST.get('priority', 2)),
                'duration_seconds': int(request.POST.get('duration_seconds', 30)),
                'frequency': request.POST.get('frequency'),
                'interval': int(request.POST.get('interval', 1)),
                'start_date': start_date,
                'end_date': end_date,
                'max_occurrences': int(request.POST.get('max_occurrences')) if request.POST.get('max_occurrences') else None,
                'campaign_name': campaign_name,
                'daily_frequency': daily_frequency,
                'total_required': total_required,
            }

            # Handle weekdays for weekly frequency
            if update_data['frequency'] == 'weekly':
                weekdays = request.POST.getlist('weekdays')
                update_data['weekdays'] = [int(day) for day in weekdays]

            # Update show assignments with scheduled_days
            # First, get existing assignment IDs to handle updates vs creates
            assignment_ids = request.POST.getlist('assignment_ids')
            show_ids = request.POST.getlist('shows')
            scheduled_times = request.POST.getlist('scheduled_times')

            validation_errors = []

            # Clear existing assignments first
            recurring_mention.recurringmentionshow_set.all().delete()

            for i, show_id in enumerate(show_ids):
                if i < len(scheduled_times) and show_id:
                    try:
                        show = Show.objects.get(id=show_id, organization=organization)
                        scheduled_time = parse_time_string(scheduled_times[i])

                        # Validate that the scheduled time is within the show's time frame
                        if not show.is_time_within_show(scheduled_time):
                            time_str = scheduled_time.strftime('%I:%M %p')
                            validation_errors.append(
                                f"Time {time_str} is outside show '{show.name}' time frame ({show.get_time_frame_display()})"
                            )
                            continue

                        # Get scheduled days for this assignment
                        # For existing assignments, use the assignment ID to get the right scheduled_days
                        scheduled_days = []
                        if i < len(assignment_ids) and assignment_ids[i]:
                            # This is an existing assignment - get its scheduled days
                            scheduled_days_key = f'scheduled_days_{assignment_ids[i]}'
                            scheduled_days = [int(day) for day in request.POST.getlist(scheduled_days_key)]
                        else:
                            # This is a new assignment - use the recurring mention's weekdays as default
                            scheduled_days = update_data.get('weekdays', [])

                        RecurringMentionShow.objects.create(
                            recurring_mention=recurring_mention,
                            show=show,
                            presenter=None,  # Will be assigned when mention is read
                            scheduled_time=scheduled_time,
                            scheduled_days=scheduled_days
                        )
                    except Show.DoesNotExist:
                        validation_errors.append(f"Invalid show selected")
                    except ValueError:
                        validation_errors.append(f"Invalid time format: {scheduled_times[i]}")

            if validation_errors:
                for error in validation_errors:
                    messages.error(request, error)
                # Don't proceed with update if there are validation errors
                # Re-render the form with errors
                clients = Client.objects.filter(organization=organization, is_active=True)
                shows = Show.objects.filter(organization=organization, is_active=True)
                existing_assignments = recurring_mention.recurringmentionshow_set.select_related('show').all()
                weekday_choices = [
                    (0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'),
                    (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')
                ]
                context = {
                    'recurring_mention': recurring_mention,
                    'clients': clients,
                    'shows': shows,
                    'existing_assignments': existing_assignments,
                    'weekday_choices': weekday_choices,
                }
                return render(request, 'mentions/recurring_mention_form.html', context)

            # Update and regenerate mentions
            RecurringMentionService.update_recurring_pattern(recurring_mention, **update_data)

            messages.success(request, 'Recurring mention updated successfully!')
            return redirect('mentions:recurring_mentions')

        except Exception as e:
            messages.error(request, f'Error updating recurring mention: {str(e)}')

    # Get form data for editing
    clients = Client.objects.filter(organization=organization, is_active=True)
    shows = Show.objects.filter(organization=organization, is_active=True)

    # Get existing show assignments for editing
    existing_assignments = recurring_mention.recurringmentionshow_set.select_related('show').all()

    # Weekday choices for template
    weekday_choices = [
        (0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'),
        (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')
    ]

    context = {
        'recurring_mention': recurring_mention,
        'clients': clients,
        'shows': shows,
        'existing_assignments': existing_assignments,
        'weekday_choices': weekday_choices,
    }
    return render(request, 'mentions/recurring_mention_form.html', context)


@login_required
def recurring_mention_delete(request, pk):
    """Delete recurring mention"""
    from .models import RecurringMention

    organization = get_current_organization(request)

    if organization:
        recurring_mention = get_object_or_404(
            RecurringMention,
            pk=pk,
            client__organization=organization
        )
    else:
        recurring_mention = get_object_or_404(RecurringMention, pk=pk)

    if request.method == 'POST':
        # Delete future unread mentions
        future_mentions = Mention.objects.filter(
            title=recurring_mention.title,
            client=recurring_mention.client,
            status__in=['pending', 'scheduled']
        )

        for mention in future_mentions:
            if not mention.mentionreading_set.filter(actual_read_time__isnull=False).exists():
                mention.delete()

        recurring_mention.delete()
        messages.success(request, 'Recurring mention deleted successfully!')
        return redirect('mentions:recurring_mentions')

    context = {
        'recurring_mention': recurring_mention,
    }
    return render(request, 'mentions/recurring_mention_confirm_delete.html', context)


@login_required
def conflict_detection(request):
    """Detect and display scheduling conflicts"""
    organization = get_current_organization(request)

    # Detect conflicts for the organization
    conflicts = ConflictDetectionService.detect_conflicts(organization=organization)

    # Group conflicts by type
    conflict_types = {
        'time_overlap': [],
        'show_capacity': [],
        'presenter_availability': []
    }

    for conflict in conflicts:
        # Map the conflict type to the correct key
        conflict_type = conflict['type']
        if conflict_type == 'show_capacity_exceeded':
            conflict_types['show_capacity'].append(conflict)
        elif conflict_type in ['no_presenter_available', 'invalid_show_day']:
            conflict_types['presenter_availability'].append(conflict)
        else:
            conflict_types[conflict_type].append(conflict)

    context = {
        'conflicts': conflicts,
        'conflict_types': conflict_types,
        'organization': organization,
    }
    return render(request, 'mentions/conflict_detection.html', context)


@login_required
def bulk_schedule(request):
    """Bulk scheduling interface"""
    organization = get_current_organization(request)

    if request.method == 'POST':
        # Handle bulk scheduling
        mention_ids = request.POST.getlist('mentions')
        show_id = request.POST.get('show')
        presenter_id = request.POST.get('presenter')
        start_date = request.POST.get('start_date')
        start_time = request.POST.get('start_time')
        interval_minutes = int(request.POST.get('interval_minutes', 30))

        try:
            # Validate show and presenter belong to the organization
            if organization:
                show = Show.objects.get(pk=show_id, organization=organization)
                presenter = Presenter.objects.get(pk=presenter_id, organization=organization)
            else:
                show = Show.objects.get(pk=show_id)
                presenter = Presenter.objects.get(pk=presenter_id)

            scheduled_count = 0
            current_datetime = datetime.combine(
                datetime.strptime(start_date, '%Y-%m-%d').date(),
                parse_time_string(start_time)
            )

            for mention_id in mention_ids:
                try:
                    mention = Mention.objects.get(
                        pk=mention_id,
                        client__organization=organization,
                        status='scheduled',
                        mentionreading__isnull=True
                    )

                    # Check for conflicts
                    reading = MentionReading(
                        mention=mention,
                        show=show,
                        presenter=None,  # Will be assigned when actually read
                        scheduled_date=current_datetime.date(),
                        scheduled_time=current_datetime.time()
                    )

                    if not reading.has_conflicts():
                        reading.save()
                        # Note: Mention status remains 'scheduled' - no need to update it
                        scheduled_count += 1

                        # Move to next time slot
                        current_datetime += timedelta(minutes=interval_minutes)

                except Mention.DoesNotExist:
                    continue

            messages.success(request, f'Successfully scheduled {scheduled_count} mentions.')
            return redirect('mentions:calendar')

        except (Show.DoesNotExist, Presenter.DoesNotExist):
            messages.error(request, 'Invalid show or presenter selected.')

    # Get approved mentions that haven't been scheduled to specific time slots yet
    unscheduled_mentions = Mention.objects.filter(
        client__organization=organization,
        status='scheduled',
        mentionreading__isnull=True
    ).select_related('client')

    # Get shows and presenters
    shows = Show.objects.filter(organization=organization, is_active=True)
    presenters = Presenter.objects.filter(organization=organization, is_active=True)

    context = {
        'unscheduled_mentions': unscheduled_mentions,
        'shows': shows,
        'presenters': presenters,
        'organization': organization,
    }
    return render(request, 'mentions/bulk_schedule.html', context)


@login_required
def mention_analytics(request):
    """Analytics and reporting for mentions"""
    organization = get_current_organization(request)

    # Date range filter
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)

    if request.GET.get('start_date'):
        start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
    if request.GET.get('end_date'):
        end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()

    # Get mentions in date range
    mentions = Mention.objects.filter(
        client__organization=organization,
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    # Calculate statistics
    stats = {
        'total_mentions': mentions.count(),
        'pending': mentions.filter(status='pending').count(),
        'scheduled': mentions.filter(status='scheduled').count(),
        'read': mentions.filter(status='read').count(),
        'cancelled': mentions.filter(status='cancelled').count(),
    }

    # Client breakdown
    client_stats = mentions.values('client__name').annotate(
        count=models.Count('id')
    ).order_by('-count')[:10]

    # Daily breakdown
    daily_stats = mentions.extra(
        select={'day': 'date(created_at)'}
    ).values('day').annotate(
        count=models.Count('id')
    ).order_by('day')

    # Priority breakdown
    priority_stats = mentions.values('priority').annotate(
        count=models.Count('id')
    ).order_by('priority')

    # Show performance
    show_stats = MentionReading.objects.filter(
        mention__client__organization=organization,
        scheduled_date__gte=start_date,
        scheduled_date__lte=end_date
    ).values('show__name').annotate(
        total_readings=models.Count('id'),
        completed_readings=models.Count('id', filter=models.Q(actual_read_time__isnull=False))
    ).order_by('-total_readings')[:10]

    context = {
        'stats': stats,
        'client_stats': client_stats,
        'daily_stats': daily_stats,
        'priority_stats': priority_stats,
        'show_stats': show_stats,
        'start_date': start_date,
        'end_date': end_date,
        'organization': organization,
    }
    return render(request, 'mentions/analytics.html', context)


@login_required
def auto_resolve_conflict(request, pk):
    """Auto-resolve a conflict for a specific reading"""
    if request.method == 'POST':
        try:
            organization = get_current_organization(request)

            # Get the reading with proper organization filtering
            if organization:
                reading = get_object_or_404(
                    MentionReading,
                    pk=pk,
                    mention__client__organization=organization
                )
            else:
                reading = get_object_or_404(MentionReading, pk=pk)

            print(f"Attempting to resolve conflict for reading {pk}: {reading.mention.title}")

            # Check if the reading actually has conflicts
            if not reading.has_conflicts():
                return JsonResponse({
                    'success': False,
                    'error': 'No conflicts detected for this reading.'
                })

            # Use the conflict detection service to reschedule
            success = ConflictDetectionService._reschedule_mention(reading.mention)

            if success:
                print(f"Successfully resolved conflict for reading {pk}")
                return JsonResponse({
                    'success': True,
                    'message': 'Conflict resolved successfully. The mention has been rescheduled to an available time slot.'
                })
            else:
                print(f"Failed to resolve conflict for reading {pk}")
                return JsonResponse({
                    'success': False,
                    'error': 'Could not find an alternative time slot. The mention has been moved back to pending status for manual rescheduling.'
                })

        except MentionReading.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Reading not found or you do not have permission to access it.'
            })
        except Exception as e:
            print(f"Error in auto_resolve_conflict: {e}")
            return JsonResponse({
                'success': False,
                'error': f'An error occurred while resolving the conflict: {str(e)}'
            })

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
@require_http_methods(["POST"])
def quick_approve_mention(request, pk):
    """Quick approve mention from dashboard"""
    try:
        organization = get_current_organization(request)

        if organization:
            mention = get_object_or_404(Mention, pk=pk, client__organization=organization)
        else:
            mention = get_object_or_404(Mention, pk=pk)

        if mention.status != 'pending':
            return JsonResponse({'success': False, 'error': 'Mention is not pending approval'})

        mention.status = 'approved'
        mention.approved_by = request.user
        mention.approved_at = timezone.now()
        mention.save()

        return JsonResponse({'success': True, 'message': 'Mention approved successfully'})

    except Mention.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Mention not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["POST"])
def quick_reject_mention(request, pk):
    """Quick reject mention from dashboard"""
    try:
        organization = get_current_organization(request)

        if organization:
            mention = get_object_or_404(Mention, pk=pk, client__organization=organization)
        else:
            mention = get_object_or_404(Mention, pk=pk)

        if mention.status != 'pending':
            return JsonResponse({'success': False, 'error': 'Mention is not pending approval'})

        # Get rejection reason from request
        import json
        data = json.loads(request.body) if request.body else {}
        reason = data.get('reason', '')

        mention.status = 'rejected'
        mention.approved_by = request.user
        mention.approved_at = timezone.now()
        if reason:
            mention.notes = f"{mention.notes}\n\nRejected: {reason}" if mention.notes else f"Rejected: {reason}"
        mention.save()

        return JsonResponse({'success': True, 'message': 'Mention rejected successfully'})

    except Mention.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Mention not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["POST"])
def bulk_approve_recurring_mentions(request):
    """Bulk approve all pending mentions generated from recurring patterns"""
    try:
        organization = get_current_organization(request)

        # Get all pending mentions from recurring patterns
        if organization:
            pending_mentions = Mention.objects.filter(
                client__organization=organization,
                status='pending',
                recurring_mention__isnull=False
            )
        else:
            pending_mentions = Mention.objects.filter(
                status='pending',
                recurring_mention__isnull=False
            )

        approved_count = pending_mentions.count()

        if approved_count == 0:
            return JsonResponse({
                'success': True,
                'approved_count': 0,
                'message': 'No pending mentions to approve'
            })

        # Bulk update to approved status
        pending_mentions.update(
            status='scheduled',
            approved_by=request.user,
            approved_at=timezone.now()
        )

        return JsonResponse({
            'success': True,
            'approved_count': approved_count,
            'message': f'Successfully approved {approved_count} mentions'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error approving mentions: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def approve_recurring_pending(request, pk):
    """Approve all pending mentions from a specific recurring pattern"""
    try:
        organization = get_current_organization(request)

        # Get the recurring mention
        if organization:
            recurring_mention = get_object_or_404(
                RecurringMention,
                pk=pk,
                client__organization=organization
            )
        else:
            recurring_mention = get_object_or_404(RecurringMention, pk=pk)

        # Get all pending mentions from this specific recurring pattern
        pending_mentions = Mention.objects.filter(
            recurring_mention=recurring_mention,
            status='pending'
        )

        approved_count = pending_mentions.count()

        if approved_count == 0:
            return JsonResponse({
                'success': True,
                'approved_count': 0,
                'message': 'No pending mentions to approve for this recurring pattern'
            })

        # Bulk update to approved status
        pending_mentions.update(
            status='scheduled',
            approved_by=request.user,
            approved_at=timezone.now()
        )

        return JsonResponse({
            'success': True,
            'approved_count': approved_count,
            'message': f'Successfully approved {approved_count} mentions from "{recurring_mention.title}"'
        })

    except RecurringMention.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Recurring mention not found'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error approving mentions: {str(e)}'
        })


@login_required
def get_pending_mentions_count(request):
    """Get count of pending mentions from recurring patterns"""
    try:
        organization = get_current_organization(request)

        if organization:
            count = Mention.objects.filter(
                client__organization=organization,
                status='pending',
                recurring_mention__isnull=False
            ).count()
        else:
            count = Mention.objects.filter(
                status='pending',
                recurring_mention__isnull=False
            ).count()

        return JsonResponse({
            'success': True,
            'count': count
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_permission('manage_mentions')
def reschedule_recurring_mention(request, pk):
    """Reschedule a recurring mention with audit tracking"""
    from datetime import date

    organization = get_current_organization(request)

    # Get the recurring mention
    if organization:
        recurring_mention = get_object_or_404(
            RecurringMention,
            pk=pk,
            client__organization=organization
        )
    else:
        recurring_mention = get_object_or_404(RecurringMention, pk=pk)

    if request.method == 'GET':
        # Return current mention data for the modal
        return JsonResponse({
            'success': True,
            'data': {
                'id': recurring_mention.id,
                'title': recurring_mention.title,
                'content': recurring_mention.content,
                'client': recurring_mention.client.name,
                'duration': f"{recurring_mention.duration_seconds} seconds",
                'frequency': recurring_mention.get_frequency_display(),
                'start_date': recurring_mention.start_date.strftime('%Y-%m-%d'),
                'end_date': recurring_mention.end_date.strftime('%Y-%m-%d') if recurring_mention.end_date else 'Ongoing',
                'current_date': date.today().strftime('%Y-%m-%d'),
            }
        })

    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            new_title = data.get('new_title', '').strip()
            new_content = data.get('new_content', '').strip()

            if not new_title:
                return JsonResponse({
                    'success': False,
                    'error': 'New title is required'
                })

            if not new_content:
                return JsonResponse({
                    'success': False,
                    'error': 'New content is required'
                })

            # Validate title length
            if len(new_title) < 3:
                return JsonResponse({
                    'success': False,
                    'error': 'Title must be at least 3 characters long'
                })

            if len(new_title) > 200:
                return JsonResponse({
                    'success': False,
                    'error': 'Title cannot exceed 200 characters'
                })

            # Validate content length
            if len(new_content) < 10:
                return JsonResponse({
                    'success': False,
                    'error': 'Content must be at least 10 characters long'
                })

            if len(new_content) > 2000:
                return JsonResponse({
                    'success': False,
                    'error': 'Content cannot exceed 2000 characters'
                })

            # Check if the recurring mention is active
            if not recurring_mention.is_active:
                return JsonResponse({
                    'success': False,
                    'error': 'Cannot reschedule an inactive recurring mention. Please activate it first.'
                })

            # Check if the end date has already passed
            if recurring_mention.end_date and recurring_mention.end_date < date.today():
                return JsonResponse({
                    'success': False,
                    'error': 'Cannot reschedule a recurring mention that has already ended.'
                })

            # Check if both title and content are the same as current values
            if (new_title.strip() == recurring_mention.title.strip() and
                new_content.strip() == recurring_mention.content.strip()):
                return JsonResponse({
                    'success': False,
                    'error': 'Either title or content (or both) must be different from the current values.'
                })

            with transaction.atomic():
                # 1. Capture original data for audit trail
                original_data = {
                    'title': recurring_mention.title,
                    'content': recurring_mention.content,
                    'client_id': recurring_mention.client.id,
                    'client_name': recurring_mention.client.name,
                    'frequency': recurring_mention.frequency,
                    'interval': recurring_mention.interval,
                    'weekdays': recurring_mention.weekdays,
                    'start_date': recurring_mention.start_date.isoformat(),
                    'end_date': recurring_mention.end_date.isoformat() if recurring_mention.end_date else None,
                    'duration_seconds': recurring_mention.duration_seconds,
                    'priority': recurring_mention.priority,
                    'is_active': recurring_mention.is_active,
                    'show_assignments': [
                        {
                            'show_id': assignment.show.id,
                            'show_name': assignment.show.name,
                            'scheduled_time': assignment.scheduled_time.strftime('%H:%M'),
                            'scheduled_days': assignment.scheduled_days,
                        }
                        for assignment in recurring_mention.recurringmentionshow_set.all()
                    ]
                }

                # 2. Create audit log entry
                MentionAuditLog.log_change(
                    change_type='reschedule',
                    description=f'Rescheduled recurring mention "{recurring_mention.title}" with new title and content',
                    original_data=original_data,
                    new_data={
                        'new_title': new_title,
                        'new_content': new_content,
                        'new_start_date': date.today().isoformat()
                    },
                    recurring_mention=recurring_mention,
                    changed_by=request.user,
                    metadata={
                        'reschedule_reason': 'Title and content update via reschedule workflow',
                        'original_title': recurring_mention.title,
                        'original_content_preview': recurring_mention.content[:100] + '...' if len(recurring_mention.content) > 100 else recurring_mention.content,
                        'new_title': new_title,
                        'new_content_preview': new_content[:100] + '...' if len(new_content) > 100 else new_content,
                    },
                    request=request
                )

                # 3. Update the recurring mention with new title, content and start date
                recurring_mention.title = new_title
                recurring_mention.content = new_content
                recurring_mention.start_date = date.today()  # Set current date as new start date
                # Keep end_date unchanged as per requirements
                # Keep all other settings (frequency, interval, weekdays, shows) unchanged
                recurring_mention.save()

                # 4. Update all future mentions that haven't been read yet
                # Find all mentions generated from this recurring pattern that are still pending or scheduled
                future_mentions = Mention.objects.filter(
                    recurring_mention=recurring_mention,
                    status__in=['pending', 'scheduled']
                ).exclude(
                    # Exclude mentions that have already been read
                    mentionreading__actual_read_time__isnull=False
                )

                updated_count = 0
                for mention in future_mentions:
                    # Create audit log for each individual mention update
                    MentionAuditLog.log_change(
                        change_type='content_update',
                        description=f'Title and content updated due to recurring mention reschedule',
                        original_data={
                            'title': mention.title,
                            'content': mention.content
                        },
                        new_data={
                            'title': new_title,
                            'content': new_content
                        },
                        mention=mention,
                        changed_by=request.user,
                        metadata={
                            'updated_via_reschedule': True,
                            'recurring_mention_id': recurring_mention.id,
                        },
                        request=request
                    )

                    # Update the mention title and content
                    mention.title = new_title
                    mention.content = new_content
                    mention.save()
                    updated_count += 1

                return JsonResponse({
                    'success': True,
                    'message': f'Recurring mention rescheduled successfully! Updated title and content for {updated_count} future mentions.',
                    'updated_mentions_count': updated_count,
                    'new_start_date': date.today().strftime('%Y-%m-%d'),
                    'new_title': new_title,
                    'new_content': new_content,
                })

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            })
        except Exception as e:
            logger.error(f"Error rescheduling recurring mention {pk}: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': f'An error occurred while rescheduling: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'error': 'Invalid request method'
    })


@login_required
@require_permission('manage_mentions')
def replace_recurring_mention(request, pk):
    """Replace current recurring mention with a new one using the same schedule pattern"""
    from datetime import date

    organization = get_current_organization(request)

    # Get the current recurring mention
    if organization:
        current_mention = get_object_or_404(
            RecurringMention,
            pk=pk,
            client__organization=organization
        )
    else:
        current_mention = get_object_or_404(RecurringMention, pk=pk)

    if request.method == 'GET':
        # Return current mention data and schedule pattern for the modal
        pattern_summary = SchedulePatternService.get_schedule_pattern_summary(current_mention)

        return JsonResponse({
            'success': True,
            'data': {
                'id': current_mention.id,
                'title': current_mention.title,
                'content': current_mention.content,
                'client': current_mention.client.name,
                'duration': f"{current_mention.duration_seconds} seconds",
                'frequency': pattern_summary['frequency_text'],
                'shows': pattern_summary['shows_text'],
                'start_date': current_mention.start_date.strftime('%Y-%m-%d'),
                'end_date': current_mention.end_date.strftime('%Y-%m-%d') if current_mention.end_date else 'Ongoing',
                'current_date': date.today().strftime('%Y-%m-%d'),
            }
        })

    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            new_title = data.get('title', '').strip()
            new_content = data.get('content', '').strip()
            new_duration = data.get('duration_seconds')

            # Validation
            if not new_title:
                return JsonResponse({
                    'success': False,
                    'error': 'Title is required'
                })

            if not new_content:
                return JsonResponse({
                    'success': False,
                    'error': 'Content is required'
                })

            if len(new_title) < 3 or len(new_title) > 200:
                return JsonResponse({
                    'success': False,
                    'error': 'Title must be between 3 and 200 characters'
                })

            if len(new_content) < 10 or len(new_content) > 2000:
                return JsonResponse({
                    'success': False,
                    'error': 'Content must be between 10 and 2000 characters'
                })

            # Validate duration if provided
            if new_duration is not None:
                try:
                    new_duration = int(new_duration)
                    if new_duration < 10 or new_duration > 300:
                        return JsonResponse({
                            'success': False,
                            'error': 'Duration must be between 10 and 300 seconds'
                        })
                except (ValueError, TypeError):
                    return JsonResponse({
                        'success': False,
                        'error': 'Invalid duration value'
                    })
            else:
                new_duration = current_mention.duration_seconds

            # Check if the current mention is active
            if not current_mention.is_active:
                return JsonResponse({
                    'success': False,
                    'error': 'Cannot replace an inactive recurring mention'
                })

            with transaction.atomic():
                # 1. Capture original data for audit trail
                original_data = {
                    'title': current_mention.title,
                    'content': current_mention.content,
                    'client_id': current_mention.client.id,
                    'client_name': current_mention.client.name,
                    'frequency': current_mention.frequency,
                    'interval': current_mention.interval,
                    'weekdays': current_mention.weekdays,
                    'start_date': current_mention.start_date.isoformat(),
                    'end_date': current_mention.end_date.isoformat() if current_mention.end_date else None,
                    'duration_seconds': current_mention.duration_seconds,
                    'priority': current_mention.priority,
                    'is_active': current_mention.is_active,
                }

                # 2. Create audit log for termination
                MentionAuditLog.log_change(
                    change_type='reschedule',
                    description=f'Terminated recurring mention "{current_mention.title}" for replacement',
                    original_data=original_data,
                    new_data={'termination_date': date.today().isoformat(), 'reason': 'replaced_with_new_mention'},
                    recurring_mention=current_mention,
                    changed_by=request.user,
                    metadata={
                        'replacement_action': True,
                        'termination_reason': 'Replaced with new mention',
                    },
                    request=request
                )

                # 3. Terminate current mention (set end date to today)
                current_mention.end_date = date.today()
                current_mention.is_active = False
                current_mention.save()

                # 4. Create new recurring mention with same pattern
                new_mention = RecurringMention.objects.create(
                    title=new_title,
                    content=new_content,
                    client=current_mention.client,
                    frequency=current_mention.frequency,
                    interval=current_mention.interval,
                    weekdays=current_mention.weekdays.copy(),
                    monthdays=current_mention.monthdays.copy(),
                    start_date=date.today(),  # Start from today
                    end_date=current_mention.end_date,  # Use original end date
                    duration_seconds=new_duration,
                    priority=current_mention.priority,
                    max_occurrences=current_mention.max_occurrences,
                    is_active=True,
                    created_by=request.user
                )

                # 5. Copy show assignments using the service
                SchedulePatternService.copy_schedule_pattern(current_mention, new_mention)

                # 6. Generate mentions for the new recurring pattern
                from apps.mentions.services import RecurringMentionService
                try:
                    generated_mentions = RecurringMentionService.generate_upcoming_mentions(new_mention)
                    generated_count = len(generated_mentions)
                    logger.info(f"Generated {generated_count} mentions for replacement recurring mention {new_mention.id}")
                except Exception as e:
                    logger.error(f"Error generating mentions for replacement recurring mention {new_mention.id}: {str(e)}")
                    generated_count = 0

                # 7. Create audit log for new mention creation
                new_data = {
                    'title': new_mention.title,
                    'content': new_mention.content,
                    'client_id': new_mention.client.id,
                    'start_date': new_mention.start_date.isoformat(),
                    'end_date': new_mention.end_date.isoformat() if new_mention.end_date else None,
                    'duration_seconds': new_mention.duration_seconds,
                    'generated_mentions_count': generated_count,
                }

                MentionAuditLog.log_change(
                    change_type='created',
                    description=f'Created new recurring mention "{new_mention.title}" as replacement',
                    original_data={},
                    new_data=new_data,
                    recurring_mention=new_mention,
                    changed_by=request.user,
                    metadata={
                        'replacement_action': True,
                        'replaced_mention_id': current_mention.id,
                        'replaced_mention_title': current_mention.title,
                        'generated_mentions_count': generated_count,
                    },
                    request=request
                )

                return JsonResponse({
                    'success': True,
                    'message': f'Successfully replaced "{current_mention.title}" with "{new_mention.title}". The old mention was terminated today and the new mention will start from today. Generated {generated_count} mention instances.',
                    'old_mention_id': current_mention.id,
                    'new_mention_id': new_mention.id,
                    'new_start_date': date.today().strftime('%Y-%m-%d'),
                    'generated_count': generated_count,
                })

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            })
        except Exception as e:
            logger.error(f"Error replacing recurring mention {pk}: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': f'An error occurred while replacing the mention: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'error': 'Invalid request method'
    })


@login_required
@require_permission('manage_mentions')
def add_additional_mention(request, pk):
    """Add additional mention to the same recurring schedule pattern"""
    from datetime import date

    organization = get_current_organization(request)

    # Get the existing recurring mention
    if organization:
        existing_mention = get_object_or_404(
            RecurringMention,
            pk=pk,
            client__organization=organization
        )
    else:
        existing_mention = get_object_or_404(RecurringMention, pk=pk)

    if request.method == 'GET':
        # Return existing mention data and schedule pattern for the modal
        pattern_summary = SchedulePatternService.get_schedule_pattern_summary(existing_mention)

        return JsonResponse({
            'success': True,
            'data': {
                'id': existing_mention.id,
                'title': existing_mention.title,
                'content': existing_mention.content,
                'client': existing_mention.client.name,
                'duration': f"{existing_mention.duration_seconds} seconds",
                'frequency': pattern_summary['frequency_text'],
                'shows': pattern_summary['shows_text'],
                'start_date': existing_mention.start_date.strftime('%Y-%m-%d'),
                'end_date': existing_mention.end_date.strftime('%Y-%m-%d') if existing_mention.end_date else 'Ongoing',
                'current_date': date.today().strftime('%Y-%m-%d'),
            }
        })

    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            new_title = data.get('title', '').strip()
            new_content = data.get('content', '').strip()
            new_duration = data.get('duration_seconds')
            use_exact_schedule = data.get('use_exact_schedule', True)

            # Validation
            if not new_title:
                return JsonResponse({
                    'success': False,
                    'error': 'Title is required'
                })

            if not new_content:
                return JsonResponse({
                    'success': False,
                    'error': 'Content is required'
                })

            if len(new_title) < 3 or len(new_title) > 200:
                return JsonResponse({
                    'success': False,
                    'error': 'Title must be between 3 and 200 characters'
                })

            if len(new_content) < 10 or len(new_content) > 2000:
                return JsonResponse({
                    'success': False,
                    'error': 'Content must be between 10 and 2000 characters'
                })

            # Validate duration if provided
            if new_duration is not None:
                try:
                    new_duration = int(new_duration)
                    if new_duration < 10 or new_duration > 300:
                        return JsonResponse({
                            'success': False,
                            'error': 'Duration must be between 10 and 300 seconds'
                        })
                except (ValueError, TypeError):
                    return JsonResponse({
                        'success': False,
                        'error': 'Invalid duration value'
                    })
            else:
                new_duration = existing_mention.duration_seconds

            # Check if the existing mention is active
            if not existing_mention.is_active:
                return JsonResponse({
                    'success': False,
                    'error': 'Cannot add to an inactive recurring mention schedule'
                })

            with transaction.atomic():
                # 1. Create new recurring mention with same pattern
                new_mention = RecurringMention.objects.create(
                    title=new_title,
                    content=new_content,
                    client=existing_mention.client,
                    frequency=existing_mention.frequency,
                    interval=existing_mention.interval,
                    weekdays=existing_mention.weekdays.copy(),
                    monthdays=existing_mention.monthdays.copy(),
                    start_date=date.today(),  # Start from today
                    end_date=existing_mention.end_date,  # Use same end date
                    duration_seconds=new_duration,
                    priority=existing_mention.priority,
                    max_occurrences=existing_mention.max_occurrences,
                    is_active=True,
                    created_by=request.user
                )

                # 2. Copy show assignments if using exact schedule
                if use_exact_schedule:
                    SchedulePatternService.copy_schedule_pattern(existing_mention, new_mention)

                    # 3. Detect potential conflicts
                    conflicts = SchedulePatternService.detect_schedule_conflicts(
                        new_mention,
                        [existing_mention]
                    )

                    conflict_warnings = []
                    for conflict in conflicts:
                        conflict_warnings.append(conflict['message'])

                # 4. Generate mentions for the new recurring pattern
                from apps.mentions.services import RecurringMentionService
                try:
                    generated_mentions = RecurringMentionService.generate_upcoming_mentions(new_mention)
                    generated_count = len(generated_mentions)
                    logger.info(f"Generated {generated_count} mentions for new additional recurring mention {new_mention.id}")
                except Exception as e:
                    logger.error(f"Error generating mentions for new recurring mention {new_mention.id}: {str(e)}")
                    generated_count = 0

                # 5. Create audit log for new mention creation
                new_data = {
                    'title': new_mention.title,
                    'content': new_mention.content,
                    'client_id': new_mention.client.id,
                    'start_date': new_mention.start_date.isoformat(),
                    'end_date': new_mention.end_date.isoformat() if new_mention.end_date else None,
                    'duration_seconds': new_mention.duration_seconds,
                    'use_exact_schedule': use_exact_schedule,
                    'generated_mentions_count': generated_count,
                }

                MentionAuditLog.log_change(
                    change_type='created',
                    description=f'Added additional recurring mention "{new_mention.title}" to existing schedule',
                    original_data={},
                    new_data=new_data,
                    recurring_mention=new_mention,
                    changed_by=request.user,
                    metadata={
                        'additional_mention_action': True,
                        'base_mention_id': existing_mention.id,
                        'base_mention_title': existing_mention.title,
                        'conflicts_detected': len(conflicts) if use_exact_schedule else 0,
                        'conflict_details': [c['message'] for c in conflicts] if use_exact_schedule else [],
                        'generated_mentions_count': generated_count,
                    },
                    request=request
                )

                response_data = {
                    'success': True,
                    'message': f'Successfully added "{new_mention.title}" to the same schedule as "{existing_mention.title}". Both mentions will now run concurrently. Generated {generated_count} mention instances.',
                    'existing_mention_id': existing_mention.id,
                    'new_mention_id': new_mention.id,
                    'new_start_date': date.today().strftime('%Y-%m-%d'),
                    'generated_count': generated_count,
                }

                # Add conflict warnings if any
                if use_exact_schedule and conflicts:
                    response_data['warnings'] = conflict_warnings
                    response_data['message'] += f' Note: {len(conflicts)} potential scheduling conflicts detected.'

                return JsonResponse(response_data)

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            })
        except Exception as e:
            logger.error(f"Error adding additional mention to {pk}: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': f'An error occurred while adding the mention: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'error': 'Invalid request method'
    })


@login_required
@require_permission('manage_mentions')
def split_recurring_schedule(request, pk):
    """Split a recurring mention schedule between two new mentions"""
    from datetime import date

    organization = get_current_organization(request)

    # Get the recurring mention to split
    if organization:
        original_mention = get_object_or_404(
            RecurringMention,
            pk=pk,
            client__organization=organization
        )
    else:
        original_mention = get_object_or_404(RecurringMention, pk=pk)

    if request.method == 'GET':
        # Return current mention data and schedule pattern for the modal
        pattern_summary = SchedulePatternService.get_schedule_pattern_summary(original_mention)

        # Get detailed schedule information
        show_assignments = original_mention.recurringmentionshow_set.all()
        schedule_details = []
        for assignment in show_assignments:
            weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            days = [weekday_names[day] for day in assignment.scheduled_days]
            schedule_details.append(f"{assignment.show.name} at {assignment.scheduled_time.strftime('%H:%M')} on {', '.join(days)}")

        return JsonResponse({
            'success': True,
            'data': {
                'id': original_mention.id,
                'title': original_mention.title,
                'content': original_mention.content,
                'client': original_mention.client.name,
                'duration': f"{original_mention.duration_seconds} seconds",
                'frequency': pattern_summary['frequency_text'],
                'shows': pattern_summary['shows_text'],
                'schedule_details': schedule_details,
                'start_date': original_mention.start_date.strftime('%Y-%m-%d'),
                'end_date': original_mention.end_date.strftime('%Y-%m-%d') if original_mention.end_date else 'Ongoing',
                'current_date': date.today().strftime('%Y-%m-%d'),
                'weekdays': original_mention.weekdays,
                'show_count': len(set(assignment.show for assignment in show_assignments)),
                'time_slot_count': show_assignments.count(),
            }
        })

    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            split_method = data.get('split_method', '').strip()

            # Extract mention A data
            mention_a_data = {
                'title': data.get('mention_a_title', '').strip(),
                'content': data.get('mention_a_content', '').strip(),
                'duration_seconds': data.get('mention_a_duration')
            }

            # Extract mention B data
            mention_b_data = {
                'title': data.get('mention_b_title', '').strip(),
                'content': data.get('mention_b_content', '').strip(),
                'duration_seconds': data.get('mention_b_duration')
            }

            # Validation
            if not split_method or split_method not in ['days', 'times', 'shows', 'weeks']:
                return JsonResponse({
                    'success': False,
                    'error': 'Please select a valid split method'
                })

            # Validate mention A
            if not mention_a_data['title'] or not mention_a_data['content']:
                return JsonResponse({
                    'success': False,
                    'error': 'Title and content are required for both mentions'
                })

            # Validate mention B
            if not mention_b_data['title'] or not mention_b_data['content']:
                return JsonResponse({
                    'success': False,
                    'error': 'Title and content are required for both mentions'
                })

            # Validate title lengths
            for label, data_dict in [('A', mention_a_data), ('B', mention_b_data)]:
                if len(data_dict['title']) < 3 or len(data_dict['title']) > 200:
                    return JsonResponse({
                        'success': False,
                        'error': f'Title for mention {label} must be between 3 and 200 characters'
                    })

                if len(data_dict['content']) < 10 or len(data_dict['content']) > 2000:
                    return JsonResponse({
                        'success': False,
                        'error': f'Content for mention {label} must be between 10 and 2000 characters'
                    })

                # Validate duration if provided
                if data_dict['duration_seconds'] is not None:
                    try:
                        duration = int(data_dict['duration_seconds'])
                        if duration < 10 or duration > 300:
                            return JsonResponse({
                                'success': False,
                                'error': f'Duration for mention {label} must be between 10 and 300 seconds'
                            })
                        data_dict['duration_seconds'] = duration
                    except (ValueError, TypeError):
                        return JsonResponse({
                            'success': False,
                            'error': f'Invalid duration value for mention {label}'
                        })
                else:
                    data_dict['duration_seconds'] = original_mention.duration_seconds

            # Check if the original mention is active
            if not original_mention.is_active:
                return JsonResponse({
                    'success': False,
                    'error': 'Cannot split an inactive recurring mention'
                })

            # Check if end date has passed
            if original_mention.end_date and original_mention.end_date < date.today():
                return JsonResponse({
                    'success': False,
                    'error': 'Cannot split a recurring mention that has already ended'
                })

            with transaction.atomic():
                # Perform the split using the service
                split_result = ScheduleSplittingService.split_schedule(
                    original_mention=original_mention,
                    split_method=split_method,
                    mention_a_data=mention_a_data,
                    mention_b_data=mention_b_data,
                    user=request.user
                )

                if not split_result['success']:
                    return JsonResponse({
                        'success': False,
                        'error': split_result['error']
                    })

                response_data = {
                    'success': True,
                    'message': f'Successfully split "{original_mention.title}" into two mentions using {split_method} method.',
                    'original_mention_id': original_mention.id,
                    'mention_a_id': split_result['mention_a'].id,
                    'mention_a_title': split_result['mention_a'].title,
                    'mention_b_id': split_result['mention_b'].id,
                    'mention_b_title': split_result['mention_b'].title,
                    'split_method': split_method,
                    'new_start_date': date.today().strftime('%Y-%m-%d'),
                }

                # Add warnings if any
                if split_result.get('warnings'):
                    response_data['warnings'] = split_result['warnings']
                    response_data['message'] += f' Note: {len(split_result["warnings"])} warnings detected.'

                return JsonResponse(response_data)

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            })
        except Exception as e:
            logger.error(f"Error splitting recurring schedule {pk}: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': f'An error occurred while splitting the schedule: {str(e)}'
            })

    return JsonResponse({
        'success': False,
        'error': 'Invalid request method'
    })


@login_required
@require_permission('manage_mentions')
def recurring_history(request):
    """Recurring mention history view with filtering and search"""
    from django.db.models import Q, Count, Case, When, IntegerField
    from datetime import datetime, timedelta

    organization = get_current_organization(request)
    if not organization:
        messages.error(request, 'Organization context required.')
        return redirect('core:dashboard')

    # Base queryset - all recurring mentions for the organization
    queryset = RecurringMention.objects.filter(
        client__organization=organization
    ).select_related('client', 'created_by', 'status_changed_by').order_by('-created_at')

    # Get filter parameters
    status_filter = request.GET.get('status', '')
    search_query = request.GET.get('search', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # Apply status filter
    if status_filter:
        queryset = queryset.filter(status=status_filter)

    # Apply search filter
    if search_query:
        queryset = queryset.filter(
            Q(title__icontains=search_query) |
            Q(campaign_name__icontains=search_query) |
            Q(client__name__icontains=search_query) |
            Q(content__icontains=search_query)
        )

    # Apply date range filter
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            queryset = queryset.filter(start_date__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            queryset = queryset.filter(start_date__lte=date_to_obj)
        except ValueError:
            pass

    # Calculate summary statistics
    stats = {
        'total': queryset.count(),
        'active': queryset.filter(status='active').count(),
        'paused': queryset.filter(status='paused').count(),
        'ended': queryset.filter(status='ended').count(),
        'finished': queryset.filter(status='finished').count(),
        'canceled': queryset.filter(status='canceled').count(),
        'replaced': queryset.filter(status='replaced').count(),
        'split': queryset.filter(status='split').count(),
    }

    # Pagination
    paginator = Paginator(queryset, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Add computed fields to each recurring mention
    for recurring_mention in page_obj:
        # Calculate total mentions planned vs actual
        recurring_mention.total_planned = recurring_mention.total_required or 0
        recurring_mention.total_actual = recurring_mention.generated_mentions_count

        # Calculate completion percentage
        if recurring_mention.total_planned > 0:
            recurring_mention.completion_percentage = min(
                100, (recurring_mention.total_actual / recurring_mention.total_planned) * 100
            )
        else:
            recurring_mention.completion_percentage = 0

    context = {
        'page_obj': page_obj,
        'stats': stats,
        'status_filter': status_filter,
        'search_query': search_query,
        'date_from': date_from,
        'date_to': date_to,
        'organization': organization,
        'status_choices': RecurringMention.STATUS_CHOICES,
    }

    return render(request, 'mentions/recurring_history.html', context)


@login_required
@require_permission('manage_mentions')
@require_http_methods(["POST"])
def update_recurring_status(request, pk):
    """Update the status of a recurring mention (cancel, end, pause, etc.)"""
    organization = get_current_organization(request)
    if not organization:
        return JsonResponse({'success': False, 'error': 'Organization context required.'})

    try:
        recurring_mention = get_object_or_404(
            RecurringMention,
            pk=pk,
            client__organization=organization
        )

        data = json.loads(request.body)
        new_status = data.get('status')

        if new_status not in dict(RecurringMention.STATUS_CHOICES):
            return JsonResponse({'success': False, 'error': 'Invalid status.'})

        # Update status with audit trail
        old_status = recurring_mention.status
        recurring_mention.update_status(new_status, request.user)

        # Handle specific status changes
        if new_status in ['canceled', 'ended', 'finished']:
            # Set is_active to False for terminated statuses
            recurring_mention.is_active = False
            recurring_mention.save(update_fields=['is_active'])

            # Optionally cancel future unread mentions
            if data.get('cancel_future_mentions', False):
                from datetime import date
                future_mentions = recurring_mention.mention_set.filter(
                    mentionreading__scheduled_date__gte=date.today(),
                    mentionreading__actual_read_time__isnull=True,
                    status__in=['pending', 'scheduled']
                )
                canceled_count = future_mentions.count()
                future_mentions.update(status='canceled')
        elif new_status == 'active':
            # Reactivate if setting back to active
            recurring_mention.is_active = True
            recurring_mention.save(update_fields=['is_active'])

        return JsonResponse({
            'success': True,
            'old_status': old_status,
            'new_status': new_status,
            'message': f'Status updated from {old_status} to {new_status} successfully.'
        })

    except RecurringMention.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Recurring mention not found.'})
    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'Invalid JSON data.'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'Error updating status: {str(e)}'})